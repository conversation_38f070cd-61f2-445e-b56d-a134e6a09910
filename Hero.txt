{"blocks": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p6c0nvs148bmozs", "tagName": "section", "meta": {"naturalWidth": 1520.800048828125}, "responsiveStyles": {"large": {"alignSelf": "stretch", "backgroundColor": "rgba(252, 252, 255, 0)", "display": "flex", "flexDirection": "column", "flexFlow": "column nowrap", "gridArea": "2 / 1 / 3 / 2", "gridRow": "2 / 3", "justifySelf": "stretch", "maxHeight": "99999px", "maxWidth": "99999px", "position": "relative"}}, "properties": {"tabindex": "-1", "aria-label": "main content"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p6j0uf8jlobmsh", "tagName": "div", "responsiveStyles": {"large": {"height": "100%", "left": "0px", "mask": "0px 50% / 100% no-repeat", "maskPosition": "0px 50%", "maskRepeat": "no-repeat", "maskSize": "100%", "overflowX": "hidden", "overflowY": "hidden", "position": "absolute", "top": "0px", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p6m01kmobhmfyjs", "tagName": "div", "responsiveStyles": {"large": {"backgroundColor": "rgb(252, 252, 255)", "height": "100%", "position": "absolute", "top": "0px", "transitionProperty": "none", "width": "100%"}}, "properties": {}, "children": []}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p6q0y80goyjm5ur", "tagName": "div", "responsiveStyles": {"large": {"height": "100%"}}, "properties": {}, "children": []}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p6t0etw6bjzldpl", "tagName": "div", "responsiveStyles": {"large": {"display": "flex", "flexDirection": "column", "flexFlow": "column nowrap", "flexGrow": "1", "overflowX": "clip", "overflowY": "clip", "position": "relative"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p6y04g3ycagye8e", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "grid", "flexGrow": "1", "gap": "0px", "gridGap": "0px", "gridRowGap": "0px", "gridTemplate": "minmax(804.968px, auto) / minmax(0px, 1fr)", "gridTemplateRows": "minmax(804.968px, auto)", "pointerEvents": "none", "position": "relative", "rowGap": "0px"}}, "properties": {"tabindex": "-1"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p730bwypnmbhwwp", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "start", "aspectRatio": "1 / 0.445097", "boxSizing": "border-box", "cursor": "pointer", "display": "grid", "gap": "0px", "gridArea": "1 / 1 / 2 / 2", "gridGap": "0px", "gridRow": "1 / 2", "gridRowGap": "0px", "gridTemplate": "1fr / 1fr", "gridTemplateRows": "1fr", "justifySelf": "end", "marginBottom": "0%", "marginLeft": "0%", "marginRight": "3.80493%", "marginTop": "8.35632%", "maxHeight": "99999px", "maxWidth": "99999px", "opacity": "0.5", "position": "relative", "rowGap": "0px", "width": "75.5032%", "transform": "matrix(1, 0, 0, 1, 0, 0)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p770tmjn4bnldtm", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "left": "0px", "position": "absolute", "right": "0px", "top": "0px"}}, "properties": {"tabindex": "0", "role": "button", "aria-label": "Decorative Play video", "aria-pressed": "true"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p7c0c65smvdstiu", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "height": "100%", "left": "0px", "mask": "0px 50% / 100% no-repeat", "maskPosition": "0px 50%", "maskRepeat": "no-repeat", "maskSize": "100%", "overflowX": "hidden", "overflowY": "hidden", "position": "absolute", "top": "0px", "width": "100%", "transform": "matrix(1, 0.000174533, -0.000174533, 1, 0, 0)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p7f05vd5p0gmn4b", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "height": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p7k0wztisci8bme", "tagName": "wix-video", "responsiveStyles": {"large": {"cursor": "pointer", "height": "100%", "position": "absolute", "top": "0px", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p7o0q4y1pb43js", "tagName": "video", "responsiveStyles": {"large": {"cursor": "pointer", "objectFit": "contain", "opacity": "0", "overflowClipMargin": "content-box", "overflowX": "clip", "overflowY": "clip", "position": "absolute", "top": "0px", "visibility": "hidden"}}, "properties": {"crossorigin": "anonymous", "aria-label": "Decorative", "playsinline": "", "preload": "auto", "muted": "", "loop": "", "src": "https://video.wixstatic.com/video/c837a6_1cf1ee5676c54e53b60e6b695c13dc55/480p/mp4/file.mp4"}, "children": []}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p7r0h0qqwahde6", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg id=\"svg_comp-m00pvwgl\" class=\"YTb3b4\" style=\"\n  cursor: pointer;\n  height: 0px;\n  left: 0px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  position: absolute;\n  top: 0px;\n  width: 0px;\"><defs><filter id=\"tvMonotoneLight-comp-m00pvwgl\" color-interpolation-filters=\"sRGB\">\n    <feComponentTransfer result=\"srcRGB\"></feComponentTransfer>\n    <feComponentTransfer><feFuncR type=\"linear\" slope=\"1\"></feFuncR><feFuncG type=\"linear\" slope=\"1\"></feFuncG><feFuncB type=\"linear\" slope=\"1\"></feFuncB></feComponentTransfer>\n<feComponentTransfer><feFuncR type=\"linear\" slope=\"1\" intercept=\"0\"></feFuncR>\n<feFuncG type=\"linear\" slope=\"1\" intercept=\"0\"></feFuncG>\n<feFuncB type=\"linear\" slope=\"1\" intercept=\"0\"></feFuncB></feComponentTransfer>\n<feColorMatrix type=\"saturate\" values=\"0\"></feColorMatrix>\n<feColorMatrix type=\"matrix\" values=\"0.7098039215686274 0 0 0 0.2901960784313726 0.5294117647058824 0 0 0 0.47058823529411764 0.5450980392156863 0 0 0 0.4549019607843137 0 0 0 1 0\"></feColorMatrix>\n    <feComponentTransfer></feComponentTransfer>\n</filter></defs></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"cursor": "pointer", "height": "0px", "left": "0px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "position": "absolute", "top": "0px", "width": "0px"}}, "properties": {}, "children": []}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p7w0khmsol7js6s", "tagName": "wow-image", "responsiveStyles": {"large": {"cursor": "pointer", "height": "100%", "position": "absolute", "top": "0px", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p8000ykyc3857vae", "tagName": "", "component": {"name": "Raw:Img", "options": {"image": "https://static.wixstatic.com/media/c837a6_1cf1ee5676c54e53b60e6b695c13dc55f000.png/v1/fill/w_1148,h_439,al_c,q_90,usm_0.33_1.00_0.00,enc_avif,quality_auto/c837a6_1cf1ee5676c54e53b60e6b695c13dc55f000.png"}}, "meta": {"originalAspectRatio": 0.4451219512195122}, "responsiveStyles": {"large": {"aspectRatio": "auto 160 / 90", "cursor": "pointer", "display": "inline", "filter": "url(\"#tvMonotoneLight-comp-m00pvwgl\")", "height": "511px", "maxWidth": "100%", "objectFit": "contain", "overflowClipMargin": "content-box", "overflowX": "clip", "overflowY": "clip", "width": "1148px"}}, "properties": {"alt": "", "width": "160", "height": "90", "fetchpriority": "high"}, "children": []}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p860aqswddpifvl", "tagName": "canvas", "responsiveStyles": {"large": {"aspectRatio": "auto 1188 / 454", "cursor": "pointer", "display": "inline", "height": "100%", "objectFit": "contain", "opacity": "0", "overflowClipMargin": "content-box", "overflowX": "clip", "overflowY": "clip", "position": "relative", "width": "100%"}}, "properties": {"aria-label": "Decorative", "role": "presentation", "width": "1188", "height": "454"}, "children": []}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p8b03hhyiciyitc", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "center", "cursor": "pointer", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "height": "60px", "justifySelf": "center", "marginLeft": "0%", "maxWidth": "99999px", "opacity": "0.8", "position": "relative", "width": "60px", "transform": "matrix(1, 0, 0, 1, 0, 0)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p8f0n9xz8g7lk9c", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p8j08b8pijvnikc", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "opacity": "0", "transitionDuration": "0.3s, 0.1s", "transitionTimingFunction": "cubic-bezier(0.12, 0, 0.39, 0), cubic-bezier(0.61, 1, 0.88, 1)", "transform": "matrix(0.95, 0, 0, 0.95, 0, 0)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p8m0gzwzmvjwccs", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 40 40\" xmlns=\"http://www.w3.org/2000/svg\" style=\"\n  cursor: pointer;\n  display: inline;\n  height: 100%;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  width: 100%;\"><rect x=\"13\" y=\"10\" width=\"5\" height=\"20\" data-color=\"1\" fill=\"white\" data-testid=\"pause-rect\"></rect><rect x=\"22\" y=\"10\" width=\"5\" height=\"20\" data-color=\"1\" fill=\"white\" data-testid=\"pause-rect\"></rect></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"cursor": "pointer", "display": "inline", "height": "100%", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "width": "100%"}}, "properties": {"width": "100%", "height": "100%", "viewBox": "0 0 40 40", "xmlns": "http://www.w3.org/2000/svg"}, "children": []}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p8q09xpe9t3qqto", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "left": "0px", "opacity": "0.99", "position": "absolute", "right": "0px", "top": "0px", "transitionDuration": "0.15s, 0.5s", "transitionProperty": "transform, opacity", "transitionTimingFunction": "cubic-bezier(0.12, 0, 0.39, 0), cubic-bezier(0.61, 1, 0.88, 1)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p8v0szw8ogo4fsk", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "left": "0px", "opacity": "0.99", "position": "absolute", "right": "0px", "top": "0px", "transitionDuration": "0.15s, 0.5s", "transitionProperty": "transform, opacity", "transitionTimingFunction": "cubic-bezier(0.12, 0, 0.39, 0), cubic-bezier(0.61, 1, 0.88, 1)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p8y0dpf6txrop8f", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg preserveAspectRatio=\"xMidYMid meet\" data-bbox=\"10 10 62 62\" viewBox=\"0 0 82 82\" height=\"82\" width=\"82\" xmlns=\"http://www.w3.org/2000/svg\" data-type=\"color\" role=\"presentation\" aria-hidden=\"true\" aria-label=\"\" style=\"\n  bottom: 0px;\n  cursor: pointer;\n  height: 82px;\n  left: 0px;\n  margin-bottom: auto;\n  margin-left: auto;\n  margin-right: auto;\n  margin-top: auto;\n  overflow-clip-margin: content-box;\n  position: absolute;\n  right: 0px;\n  top: 0px;\n  vector-effect: non-scaling-stroke;\n  width: 82px;\"><defs><style>#comp-m00pvwgn5 svg [data-color=\"1\"] {fill: #000000;}</style></defs>\n    <g>\n        <path d=\"M41 10c-17.121 0-31 13.879-31 31 0 17.121 13.879 31 31 31 17.121 0 31-13.879 31-31 0-17.121-13.879-31-31-31zm2.008 35.268l-7.531 4.268V32.465l7.531 4.268L50.539 41l-7.531 4.268z\" fill=\"#2B328C\" data-color=\"1\"></path>\n    </g>\n</svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "height": "82px", "left": "0px", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "overflowClipMargin": "content-box", "position": "absolute", "right": "0px", "top": "0px", "vectorEffect": "non-scaling-stroke", "width": "82px"}}, "properties": {"preserveAspectRatio": "xMidYMid meet", "viewBox": "0 0 82 82", "height": "82", "width": "82", "xmlns": "http://www.w3.org/2000/svg", "role": "presentation", "aria-hidden": "true", "aria-label": ""}, "children": []}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p9203rrbzgkmd4t", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "end", "cursor": "pointer", "display": "none", "fill": "rgb(255, 255, 255)", "filter": "drop-shadow(rgb(0, 0, 0) 0px 0px 1px)", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "height": "44px", "justifySelf": "end", "marginBottom": "2px", "marginLeft": "2px", "marginRight": "2px", "position": "relative", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "transitionDelay": "0.2s", "transitionDuration": "0.2s", "transitionProperty": "opacity", "transitionTimingFunction": "cubic-bezier(0.12, 0, 0.39, 0)"}}, "properties": {"tabindex": "0", "role": "button", "aria-label": "Mute", "aria-pressed": "true"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p960rwljsr0ko6", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "fill": "rgb(255, 255, 255)", "marginBottom": "7px", "marginLeft": "8px", "marginRight": "10px", "marginTop": "5px", "opacity": "0", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "width": "48px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p9a0qfewl857cw", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "fill": "rgb(255, 255, 255)", "left": "0px", "marginBottom": "7px", "marginLeft": "8px", "marginRight": "10px", "marginTop": "5px", "opacity": "0", "position": "absolute", "right": "0px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "top": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p9e04s9rosgnjae", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg preserveAspectRatio=\"xMidYMid meet\" data-bbox=\"7 5.999 16 18.001\" data-type=\"shape\" xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" viewBox=\"0 0 30 30\" role=\"presentation\" aria-hidden=\"true\" aria-label=\"\" style=\"\n  bottom: 0px;\n  cursor: pointer;\n  fill: rgb(255, 255, 255);\n  height: 30px;\n  left: 0px;\n  margin-bottom: auto;\n  margin-left: auto;\n  margin-right: auto;\n  margin-top: auto;\n  overflow-clip-margin: content-box;\n  position: absolute;\n  right: 0px;\n  stroke: rgb(0, 0, 0);\n  stroke-width: 0px;\n  top: 0px;\n  vector-effect: non-scaling-stroke;\n  width: 30px;\">\n    <g>\n        <defs>\n            <filter id=\"4e358e3c-c4cc-411c-8da6-747bbbb99bfa_audioOn-comp-m00pvwgo6\" height=\"200%\" width=\"200%\" y=\"-50%\" x=\"-50%\">\n                <feOffset result=\"out-offset\" in=\"SourceAlpha\"></feOffset>\n                <feGaussianBlur result=\"out-blur\" in=\"out-offset\" stdDeviation=\"2\"></feGaussianBlur>\n                <feColorMatrix result=\"out-matrix\" in=\"out-blur\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0\"></feColorMatrix>\n                <feMerge>\n                    <feMergeNode in=\"out-matrix\"></feMergeNode>\n                    <feMergeNode in=\"SourceGraphic\"></feMergeNode>\n                </feMerge>\n            </filter>\n        </defs>\n        <path filter=\"url(#4e358e3c-c4cc-411c-8da6-747bbbb99bfa_audioOn-comp-m00pvwgo6)\" d=\"M23 6.616a.625.625 0 0 0-.727-.609l-10.241 1.54a.62.62 0 0 0-.535.609l-.006 10.016c0 .892-.276 1.319-1.971 1.319C7.646 19.49 7 20.631 7 21.748 7 22.655 7.507 24 9.516 24c2.249 0 3.236-1.44 3.236-2.713l.006-9.719 8.98-1.454v6.87c-.045.763-.401 1.13-1.973 1.13-1.874 0-2.52 1.141-2.52 2.258 0 .907.507 2.252 2.516 2.252 2.249 0 3.236-1.44 3.236-2.713L23 6.616z\"></path>\n    </g>\n</svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "fill": "rgb(255, 255, 255)", "height": "30px", "left": "0px", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "overflowClipMargin": "content-box", "position": "absolute", "right": "0px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "top": "0px", "vectorEffect": "non-scaling-stroke", "width": "30px"}}, "properties": {"preserveAspectRatio": "xMidYMid meet", "xmlns": "http://www.w3.org/2000/svg", "width": "30", "height": "30", "viewBox": "0 0 30 30", "role": "presentation", "aria-hidden": "true", "aria-label": ""}, "children": []}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p9i0q3h5vv9o8tc", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "fill": "rgb(255, 255, 255)", "marginBottom": "7px", "marginLeft": "8px", "marginRight": "10px", "marginTop": "5px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "width": "48px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p9m02j6468c8cp5", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "fill": "rgb(255, 255, 255)", "left": "0px", "marginBottom": "7px", "marginLeft": "8px", "marginRight": "10px", "marginTop": "5px", "position": "absolute", "right": "0px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "top": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p9q05nz59e6axyo", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg preserveAspectRatio=\"xMidYMid meet\" data-bbox=\"5.726 5.999 21.997 18.001\" data-type=\"shape\" xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" viewBox=\"0 0 30 30\" role=\"presentation\" aria-hidden=\"true\" aria-label=\"\" style=\"\n  bottom: 0px;\n  cursor: pointer;\n  fill: rgb(255, 255, 255);\n  height: 30px;\n  left: 0px;\n  margin-bottom: auto;\n  margin-left: auto;\n  margin-right: auto;\n  margin-top: auto;\n  overflow-clip-margin: content-box;\n  position: absolute;\n  right: 0px;\n  stroke: rgb(0, 0, 0);\n  stroke-width: 0px;\n  top: 0px;\n  vector-effect: non-scaling-stroke;\n  width: 30px;\">\n    <g>\n        <defs>\n            <filter id=\"b0f9f707-af61-4336-bcea-c2fee482e562_audioOff-comp-m00pvwgo6\" height=\"200%\" width=\"200%\" y=\"-50%\" x=\"-50%\">\n                <feOffset result=\"out-offset\" in=\"SourceAlpha\"></feOffset>\n                <feGaussianBlur result=\"out-blur\" in=\"out-offset\" stdDeviation=\"2\"></feGaussianBlur>\n                <feColorMatrix result=\"out-matrix\" in=\"out-blur\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.40 0\"></feColorMatrix>\n                <feMerge>\n                    <feMergeNode in=\"out-matrix\"></feMergeNode>\n                    <feMergeNode in=\"SourceGraphic\"></feMergeNode>\n                </feMerge>\n            </filter>\n        </defs>\n        <path filter=\"url(#b0f9f707-af61-4336-bcea-c2fee482e562_audioOff-comp-m00pvwgo6)\" d=\"M27.39 17.535a.478.478 0 0 1 .306.615v.001a.51.51 0 0 1-.641.292L6.074 12.471a.478.478 0 0 1-.325-.605.505.505 0 0 1 .661-.302l20.98 5.971zm-6.211.375c1.911-.377 1.812 2.001 1.813 2.001 0 1.273-.986 2.713-3.235 2.713-2.009 0-2.515-1.345-2.515-2.252 0-1.117.646-2.258 2.519-2.258.671-.001 1.095-.141 1.418-.204zm-8.427-1.643v.013h.001l-.005 5.007c0 1.273-.985 2.713-3.233 2.713C7.506 24 7 22.655 7 21.748c0-1.117.646-2.258 2.519-2.258 1.696 0 1.972-.427 1.972-1.319l.001-1.934a.513.513 0 0 1 .512-.477h.23c.285 0 .518.228.518.507zm-.537-4.642a.666.666 0 0 1-.506-.141.61.61 0 0 1-.22-.468l.006-2.86c0-.304.227-.562.535-.609l10.238-1.54a.629.629 0 0 1 .726.609L23 13.591c0 .013-.006.024-.007.036a.49.49 0 0 1-.094.248.516.516 0 0 1-.416.222h-.229a.51.51 0 0 1-.517-.505l-.004-3.479-9.518 1.512z\"></path>\n    </g>\n</svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "fill": "rgb(255, 255, 255)", "height": "30px", "left": "0px", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "overflowClipMargin": "content-box", "position": "absolute", "right": "0px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "top": "0px", "vectorEffect": "non-scaling-stroke", "width": "30px"}}, "properties": {"preserveAspectRatio": "xMidYMid meet", "xmlns": "http://www.w3.org/2000/svg", "width": "30", "height": "30", "viewBox": "0 0 30 30", "role": "presentation", "aria-hidden": "true", "aria-label": ""}, "children": []}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p9u099mvdplb12", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "start", "boxSizing": "border-box", "display": "flex", "flexDirection": "column", "flexFlow": "column nowrap", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "justifySelf": "start", "marginBottom": "0%", "marginLeft": "5.99976%", "marginRight": "0%", "marginTop": "5.21851%", "position": "relative", "width": "31.6382%"}}, "properties": {"role": "", "dir": "ltr"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88p9y0vgy1lndmsw", "tagName": "div", "responsiveStyles": {"large": {"backgroundColor": "rgba(252, 252, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgba(38, 38, 39, 0)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "bottom": "0px", "left": "0px", "position": "absolute", "right": "0px", "top": "0px"}}, "properties": {}, "children": []}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pa20ooyrv5td7o8", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "flex-start", "marginBottom": "59.2638%", "marginLeft": "0%", "marginRight": "0%", "marginTop": "0%", "maxWidth": "99999px", "order": "1", "overflowWrap": "break-word", "position": "relative", "textShadow": "rgba(0, 0, 0, 0) 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px", "width": "100%", "wordWrap": "break-word"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pa509am2coul93d", "tagName": "h1", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "color": "rgb(38, 38, 39)", "fontFamily": "bitter, bitter, serif", "fontSize": "50.4px", "lineHeight": "50.4px", "outlineColor": "rgb(38, 38, 39)", "overflowWrap": "break-word", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textShadow": "rgba(0, 0, 0, 0) 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px", "wordWrap": "break-word"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pa90epyx6chvwmw", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "color": "rgb(38, 38, 39)", "display": "inline", "fontFamily": "bitter, bitter, serif", "fontSize": "50.4px", "lineHeight": "50.4px", "outlineColor": "rgb(38, 38, 39)", "overflowWrap": "break-word", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textShadow": "rgba(0, 0, 0, 0) 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px", "wordWrap": "break-word"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "While You Wait, Others Are Winning with AI"}}}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pad03d0q59wec0w", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "flex-start", "marginBottom": "28.5187px", "marginLeft": "0%", "marginRight": "0%", "marginTop": "0%", "maxWidth": "99999px", "order": "2", "overflowWrap": "break-word", "position": "relative", "textShadow": "rgba(0, 0, 0, 0) 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px", "width": "80.2338%", "wordWrap": "break-word"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88paj08c7xhrsafjg", "tagName": "p", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "color": "rgb(38, 38, 39)", "fontFamily": "bitter, serif", "fontSize": "26.4px", "lineHeight": "31.68px", "outlineColor": "rgb(38, 38, 39)", "overflowWrap": "break-word", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textShadow": "rgba(0, 0, 0, 0) 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px", "wordWrap": "break-word"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "The Time to Act Is Now, Together We Can Transform Your Businesses"}}}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pan0hcbggad2s2u", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "flex-start", "height": "32px", "marginBottom": "0%", "marginLeft": "0%", "marginRight": "0%", "marginTop": "0%", "maxHeight": "99999px", "maxWidth": "99999px", "order": "3", "position": "relative", "width": "247px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88par<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottom": "0.8px solid rgb(92, 91, 94)", "borderBottomLeftRadius": "6px", "borderBottomRightRadius": "6px", "borderBottomStyle": "solid", "borderBottomWidth": "0.8px", "borderColor": "rgb(92, 91, 94)", "borderLeft": "0.8px solid rgb(92, 91, 94)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRadius": "6px", "borderRight": "0.8px solid rgb(92, 91, 94)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(92, 91, 94)", "borderTopLeftRadius": "6px", "borderTopRightRadius": "6px", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px", "boxSizing": "border-box", "cursor": "pointer", "height": "100%", "minHeight": "10px", "minWidth": "10px", "touchAction": "manipulation", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.2s, 0s", "transitionProperty": "all, visibility", "transitionTimingFunction": "ease, ease", "width": "100%"}}, "properties": {"target": "_self", "aria-label": "GET A FREE CONSULTATION"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pay01ylnxs5ul5k", "tagName": "span", "responsiveStyles": {"large": {"alignItems": "center", "cursor": "pointer", "display": "flex", "flexGrow": "1", "height": "100%", "justifyContent": "center", "overflowX": "hidden", "overflowY": "hidden", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.2s, 0s", "transitionProperty": "all, visibility", "transitionTimingFunction": "ease, ease", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pb60fg7vyz4igr", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(92, 91, 94)", "color": "rgb(92, 91, 94)", "cursor": "pointer", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "14px", "letterSpacing": "1.4px", "lineHeight": "14px", "marginRight": "6px", "maxWidth": "100%", "minWidth": "25.2px", "outlineColor": "rgb(92, 91, 94)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "center", "textDecoration": "rgb(92, 91, 94)", "textDecorationColor": "rgb(92, 91, 94)", "textEmphasisColor": "rgb(92, 91, 94)", "textOverflow": "ellipsis", "textWrap": "nowrap", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.2s, 0s", "transitionProperty": "all, visibility", "transitionTimingFunction": "ease, ease", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "GET A FREE CONSULTATION"}}}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pb90eb4gz5aseu6", "tagName": "span", "responsiveStyles": {"large": {"cursor": "pointer", "display": "none", "flexShrink": "0", "height": "17px", "marginLeft": "6px", "minWidth": "1px", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.2s, 0s", "transitionProperty": "all, visibility", "transitionTimingFunction": "ease, ease", "width": "17px"}}, "properties": {"aria-hidden": "true"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pbe0wioxtkw2tq", "tagName": "span", "responsiveStyles": {"large": {"cursor": "pointer", "display": "flex", "height": "17px", "width": "17px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pbi0fxa2yxdbuhf", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg data-bbox=\"20 59.5 160 81.001\" viewBox=\"0 0 200 200\" height=\"200\" width=\"200\" xmlns=\"http://www.w3.org/2000/svg\" data-type=\"shape\" style=\"\n  cursor: pointer;\n  display: flex;\n  height: 200px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  width: 200px;\">\n    <g>\n        <path d=\"M177.687 128.054L105.35 61.402a7.205 7.205 0 0 0-5.35-1.886 7.198 7.198 0 0 0-5.349 1.886l-72.338 66.652a7.165 7.165 0 0 0-.407 10.138 7.172 7.172 0 0 0 5.283 2.309c1.743 0 3.49-.629 4.872-1.902L100 75.999l67.939 62.598a7.197 7.197 0 0 0 10.155-.406 7.163 7.163 0 0 0-.407-10.137z\"></path>\n    </g>\n</svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"cursor": "pointer", "display": "flex", "height": "200px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "width": "200px"}}, "properties": {"viewBox": "0 0 200 200", "height": "200", "width": "200", "xmlns": "http://www.w3.org/2000/svg"}, "children": []}]}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2/contact"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pbm0hgovyir37g8", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "start", "aspectRatio": "1 / 0", "display": "none", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "justifySelf": "end", "marginBottom": "0%", "marginLeft": "0%", "marginRight": "5.52246%", "marginTop": "4.74976%", "maxHeight": "99999px", "maxWidth": "99999px", "position": "relative", "width": "39.9622%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pbp0q90fp05tc6l", "tagName": "div", "responsiveStyles": {"large": {"borderBottomStyle": "solid", "borderColor": "rgba(255, 255, 255, 0)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "bottom": "0px", "boxSizing": "border-box", "left": "0px", "overflowX": "hidden", "overflowY": "hidden", "position": "absolute", "right": "0px", "top": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pbt08l9uvcnb17n", "tagName": "div", "responsiveStyles": {"large": {"height": "100%", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pbx0szdoe8pthm", "tagName": "div", "responsiveStyles": {"large": {}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pc00yx2havj7sjn", "tagName": "wow-image", "responsiveStyles": {"large": {"bottom": "0px", "height": "100%", "left": "0px", "overflowX": "clip", "overflowY": "clip", "position": "absolute", "right": "0px", "top": "0px", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pc400wkm1spcyc4", "tagName": "span", "responsiveStyles": {"large": {"display": "inline"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pc708kv4tubli6p", "tagName": "", "component": {"name": "Raw:Img", "options": {"image": "https://static.wixstatic.com/media/c837a6_b93a40a80a2043e4be7724bc25cae842~mv2.png/v1/fill/w_49,h_50,al_c,q_85,usm_0.66_1.00_0.01,blur_2,enc_avif,quality_auto/Screenshot%202024-08-14%20at%2010_09_02-02.png"}}, "meta": {"originalAspectRatio": 1.0204081632653061}, "responsiveStyles": {"large": {"height": "100%", "mask": "0px 50% / 100% 100% no-repeat", "maskPosition": "0px 50%", "maskRepeat": "no-repeat", "maskSize": "100% 100%", "objectFit": "cover", "overflowClipMargin": "content-box", "overflowX": "clip", "overflowY": "clip", "width": "100%"}}, "properties": {"fetchpriority": "high", "alt": "Screenshot 2024-08-14 at 10.09.02-02.png"}, "children": []}]}]}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pcc0787eyfyp8r5", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "start", "aspectRatio": "1 / 0", "display": "none", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "justifySelf": "end", "marginBottom": "0%", "marginLeft": "0%", "marginRight": "15.3894%", "marginTop": "12.6196%", "maxHeight": "99999px", "maxWidth": "99999px", "position": "relative", "width": "42.8564%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pcf07mkdzh4ycuw", "tagName": "div", "responsiveStyles": {"large": {"borderBottomStyle": "solid", "borderColor": "rgba(255, 255, 255, 0)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "bottom": "0px", "boxSizing": "border-box", "left": "0px", "overflowX": "hidden", "overflowY": "hidden", "position": "absolute", "right": "0px", "top": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pcj0t0vhhc89t1", "tagName": "div", "responsiveStyles": {"large": {"height": "100%", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pcn05byq68ojqn8", "tagName": "div", "responsiveStyles": {"large": {}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pcq00cnvoxekrmar", "tagName": "wow-image", "responsiveStyles": {"large": {"bottom": "0px", "height": "100%", "left": "0px", "overflowX": "clip", "overflowY": "clip", "position": "absolute", "right": "0px", "top": "0px", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pct0ra0im9gp2yh", "tagName": "span", "responsiveStyles": {"large": {"display": "inline"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pcx063g85ppz8lx", "tagName": "", "component": {"name": "Raw:Img", "options": {"image": "https://static.wixstatic.com/media/c837a6_42789ae9acec4c0b81f3bafc01527381~mv2.png/v1/fill/w_49,h_35,al_c,q_85,usm_0.66_1.00_0.01,blur_2,enc_avif,quality_auto/Screenshot%202024-08-14%20at%2014_13_20-2.png"}}, "meta": {"originalAspectRatio": 0.7142857142857143}, "responsiveStyles": {"large": {"height": "100%", "mask": "0px 50% / 100% 100% no-repeat", "maskPosition": "0px 50%", "maskRepeat": "no-repeat", "maskSize": "100% 100%", "objectFit": "cover", "opacity": "0.2", "overflowClipMargin": "content-box", "overflowX": "clip", "overflowY": "clip", "width": "100%"}}, "properties": {"fetchpriority": "high", "alt": "Screenshot 2024-08-14 at 14.13.20-2.png"}, "children": []}]}]}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pd107zxl8sh5yv", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "center", "aspectRatio": "1 / 0.967874", "boxSizing": "border-box", "cursor": "pointer", "display": "grid", "gap": "0px", "gridArea": "1 / 1 / 2 / 2", "gridGap": "0px", "gridRow": "1 / 2", "gridRowGap": "0px", "gridTemplate": "1fr / 1fr", "gridTemplateRows": "1fr", "justifySelf": "end", "marginBottom": "0%", "marginLeft": "0%", "marginRight": "18.5449%", "marginTop": "0%", "maxHeight": "99999px", "maxWidth": "99999px", "opacity": "0.8", "position": "relative", "rowGap": "0px", "width": "44.2539%", "transform": "matrix(1, 0, 0, 1, 0, 0)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pd60w87aoz8wu5", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "left": "0px", "position": "absolute", "right": "0px", "top": "0px"}}, "properties": {"tabindex": "0", "role": "button", "aria-label": "Decorative Play video", "aria-pressed": "true"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pda07bczqt80ohb", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "height": "100%", "left": "0px", "mask": "0px 50% / 100% no-repeat", "maskPosition": "0px 50%", "maskRepeat": "no-repeat", "maskSize": "100%", "overflowX": "hidden", "overflowY": "hidden", "position": "absolute", "top": "0px", "width": "100%", "transform": "matrix(1, 0.000174533, -0.000174533, 1, 0, 0)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pdd09387tyzq15t", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "height": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pdh0rjbk6oie0d", "tagName": "wix-video", "responsiveStyles": {"large": {"cursor": "pointer", "height": "100%", "position": "absolute", "top": "0px", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pdl0qmwj3sbm28", "tagName": "video", "responsiveStyles": {"large": {"cursor": "pointer", "objectFit": "contain", "opacity": "0", "overflowClipMargin": "content-box", "overflowX": "clip", "overflowY": "clip", "position": "absolute", "top": "0px", "visibility": "hidden"}}, "properties": {"crossorigin": "anonymous", "aria-label": "Decorative", "playsinline": "", "preload": "auto", "muted": "", "loop": "", "src": "https://video.wixstatic.com/video/c837a6_44745c4a16b9417381ab40bee42cfa1b/720p/mp4/file.mp4"}, "children": []}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pdp0mxwct9f0sa", "tagName": "wow-image", "responsiveStyles": {"large": {"cursor": "pointer", "height": "100%", "position": "absolute", "top": "0px", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pdt09610i89ceip", "tagName": "", "component": {"name": "Raw:Img", "options": {"image": "https://static.wixstatic.com/media/c837a6_44745c4a16b9417381ab40bee42cfa1bf000.png/v1/fill/w_673,h_630,al_c,q_90,usm_0.33_1.00_0.00,enc_avif,quality_auto/c837a6_44745c4a16b9417381ab40bee42cfa1bf000.png"}}, "meta": {"originalAspectRatio": 0.9673105497771174}, "responsiveStyles": {"large": {"aspectRatio": "auto 160 / 90", "cursor": "pointer", "display": "inline", "height": "651px", "maxWidth": "100%", "objectFit": "contain", "overflowClipMargin": "content-box", "overflowX": "clip", "overflowY": "clip", "width": "673px"}}, "properties": {"alt": "", "width": "160", "height": "90", "fetchpriority": "high"}, "children": []}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pdz05ipujxz8gih", "tagName": "canvas", "responsiveStyles": {"large": {"aspectRatio": "auto 950 / 890", "cursor": "pointer", "display": "inline", "height": "100%", "objectFit": "contain", "opacity": "0", "overflowClipMargin": "content-box", "overflowX": "clip", "overflowY": "clip", "position": "relative", "width": "100%"}}, "properties": {"aria-label": "Decorative", "role": "presentation", "width": "950", "height": "890"}, "children": []}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pe50vonx1p1a4q", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "center", "cursor": "pointer", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "height": "60px", "justifySelf": "center", "marginLeft": "0%", "maxWidth": "99999px", "opacity": "0.8", "position": "relative", "width": "60px", "transform": "matrix(1, 0, 0, 1, 0, 0)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88peb05cqyirunvgt", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88peh0bidq13f6vnu", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "opacity": "0", "transitionDuration": "0.3s, 0.1s", "transitionTimingFunction": "cubic-bezier(0.12, 0, 0.39, 0), cubic-bezier(0.61, 1, 0.88, 1)", "transform": "matrix(0.95, 0, 0, 0.95, 0, 0)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pen0uwzat5ecfy", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 40 40\" xmlns=\"http://www.w3.org/2000/svg\" style=\"\n  cursor: pointer;\n  display: inline;\n  height: 100%;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  width: 100%;\"><rect x=\"13\" y=\"10\" width=\"5\" height=\"20\" data-color=\"1\" fill=\"white\" data-testid=\"pause-rect\"></rect><rect x=\"22\" y=\"10\" width=\"5\" height=\"20\" data-color=\"1\" fill=\"white\" data-testid=\"pause-rect\"></rect></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"cursor": "pointer", "display": "inline", "height": "100%", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "width": "100%"}}, "properties": {"width": "100%", "height": "100%", "viewBox": "0 0 40 40", "xmlns": "http://www.w3.org/2000/svg"}, "children": []}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pes035pjj8x8zr9", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "left": "0px", "opacity": "0.99", "position": "absolute", "right": "0px", "top": "0px", "transitionDuration": "0.15s, 0.5s", "transitionProperty": "transform, opacity", "transitionTimingFunction": "cubic-bezier(0.12, 0, 0.39, 0), cubic-bezier(0.61, 1, 0.88, 1)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pey00tq77kv0hay", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "left": "0px", "opacity": "0.99", "position": "absolute", "right": "0px", "top": "0px", "transitionDuration": "0.15s, 0.5s", "transitionProperty": "transform, opacity", "transitionTimingFunction": "cubic-bezier(0.12, 0, 0.39, 0), cubic-bezier(0.61, 1, 0.88, 1)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pf40w0jx3v89zl", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg preserveAspectRatio=\"xMidYMid meet\" data-bbox=\"10 10 62 62\" viewBox=\"0 0 82 82\" height=\"82\" width=\"82\" xmlns=\"http://www.w3.org/2000/svg\" data-type=\"color\" role=\"presentation\" aria-hidden=\"true\" aria-label=\"\" style=\"\n  bottom: 0px;\n  cursor: pointer;\n  height: 82px;\n  left: 0px;\n  margin-bottom: auto;\n  margin-left: auto;\n  margin-right: auto;\n  margin-top: auto;\n  overflow-clip-margin: content-box;\n  position: absolute;\n  right: 0px;\n  top: 0px;\n  vector-effect: non-scaling-stroke;\n  width: 82px;\"><defs><style>#comp-m02dsi54 svg [data-color=\"1\"] {fill: #000000;}</style></defs>\n    <g>\n        <path d=\"M41 10c-17.121 0-31 13.879-31 31 0 17.121 13.879 31 31 31 17.121 0 31-13.879 31-31 0-17.121-13.879-31-31-31zm2.008 35.268l-7.531 4.268V32.465l7.531 4.268L50.539 41l-7.531 4.268z\" fill=\"#2B328C\" data-color=\"1\"></path>\n    </g>\n</svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "height": "82px", "left": "0px", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "overflowClipMargin": "content-box", "position": "absolute", "right": "0px", "top": "0px", "vectorEffect": "non-scaling-stroke", "width": "82px"}}, "properties": {"preserveAspectRatio": "xMidYMid meet", "viewBox": "0 0 82 82", "height": "82", "width": "82", "xmlns": "http://www.w3.org/2000/svg", "role": "presentation", "aria-hidden": "true", "aria-label": ""}, "children": []}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pfa0xm8b1apmcd", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "end", "cursor": "pointer", "display": "none", "fill": "rgb(255, 255, 255)", "filter": "drop-shadow(rgb(0, 0, 0) 0px 0px 1px)", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "height": "44px", "justifySelf": "end", "marginBottom": "2px", "marginLeft": "2px", "marginRight": "2px", "position": "relative", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "transitionDelay": "0.2s", "transitionDuration": "0.2s", "transitionProperty": "opacity", "transitionTimingFunction": "cubic-bezier(0.12, 0, 0.39, 0)"}}, "properties": {"tabindex": "0", "role": "button", "aria-label": "Mute", "aria-pressed": "true"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pff0373igmh53uh", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "fill": "rgb(255, 255, 255)", "marginBottom": "7px", "marginLeft": "8px", "marginRight": "10px", "marginTop": "5px", "opacity": "0", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "width": "48px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pfi09l1kiaknwfe", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "fill": "rgb(255, 255, 255)", "left": "0px", "marginBottom": "7px", "marginLeft": "8px", "marginRight": "10px", "marginTop": "5px", "opacity": "0", "position": "absolute", "right": "0px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "top": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pfm0uqweozaanqe", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg preserveAspectRatio=\"xMidYMid meet\" data-bbox=\"7 5.999 16 18.001\" data-type=\"shape\" xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" viewBox=\"0 0 30 30\" role=\"presentation\" aria-hidden=\"true\" aria-label=\"\" style=\"\n  bottom: 0px;\n  cursor: pointer;\n  fill: rgb(255, 255, 255);\n  height: 30px;\n  left: 0px;\n  margin-bottom: auto;\n  margin-left: auto;\n  margin-right: auto;\n  margin-top: auto;\n  overflow-clip-margin: content-box;\n  position: absolute;\n  right: 0px;\n  stroke: rgb(0, 0, 0);\n  stroke-width: 0px;\n  top: 0px;\n  vector-effect: non-scaling-stroke;\n  width: 30px;\">\n    <g>\n        <defs>\n            <filter id=\"4e358e3c-c4cc-411c-8da6-747bbbb99bfa_audioOn-comp-m02dsi55\" height=\"200%\" width=\"200%\" y=\"-50%\" x=\"-50%\">\n                <feOffset result=\"out-offset\" in=\"SourceAlpha\"></feOffset>\n                <feGaussianBlur result=\"out-blur\" in=\"out-offset\" stdDeviation=\"2\"></feGaussianBlur>\n                <feColorMatrix result=\"out-matrix\" in=\"out-blur\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0\"></feColorMatrix>\n                <feMerge>\n                    <feMergeNode in=\"out-matrix\"></feMergeNode>\n                    <feMergeNode in=\"SourceGraphic\"></feMergeNode>\n                </feMerge>\n            </filter>\n        </defs>\n        <path filter=\"url(#4e358e3c-c4cc-411c-8da6-747bbbb99bfa_audioOn-comp-m02dsi55)\" d=\"M23 6.616a.625.625 0 0 0-.727-.609l-10.241 1.54a.62.62 0 0 0-.535.609l-.006 10.016c0 .892-.276 1.319-1.971 1.319C7.646 19.49 7 20.631 7 21.748 7 22.655 7.507 24 9.516 24c2.249 0 3.236-1.44 3.236-2.713l.006-9.719 8.98-1.454v6.87c-.045.763-.401 1.13-1.973 1.13-1.874 0-2.52 1.141-2.52 2.258 0 .907.507 2.252 2.516 2.252 2.249 0 3.236-1.44 3.236-2.713L23 6.616z\"></path>\n    </g>\n</svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "fill": "rgb(255, 255, 255)", "height": "30px", "left": "0px", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "overflowClipMargin": "content-box", "position": "absolute", "right": "0px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "top": "0px", "vectorEffect": "non-scaling-stroke", "width": "30px"}}, "properties": {"preserveAspectRatio": "xMidYMid meet", "xmlns": "http://www.w3.org/2000/svg", "width": "30", "height": "30", "viewBox": "0 0 30 30", "role": "presentation", "aria-hidden": "true", "aria-label": ""}, "children": []}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pfq0r1b26nmvmxe", "tagName": "div", "responsiveStyles": {"large": {"cursor": "pointer", "fill": "rgb(255, 255, 255)", "marginBottom": "7px", "marginLeft": "8px", "marginRight": "10px", "marginTop": "5px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "width": "48px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pft0bks32qqc4p", "tagName": "div", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "fill": "rgb(255, 255, 255)", "left": "0px", "marginBottom": "7px", "marginLeft": "8px", "marginRight": "10px", "marginTop": "5px", "position": "absolute", "right": "0px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "top": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq88pfx0v738bblsizp", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg preserveAspectRatio=\"xMidYMid meet\" data-bbox=\"5.726 5.999 21.997 18.001\" data-type=\"shape\" xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" viewBox=\"0 0 30 30\" role=\"presentation\" aria-hidden=\"true\" aria-label=\"\" style=\"\n  bottom: 0px;\n  cursor: pointer;\n  fill: rgb(255, 255, 255);\n  height: 30px;\n  left: 0px;\n  margin-bottom: auto;\n  margin-left: auto;\n  margin-right: auto;\n  margin-top: auto;\n  overflow-clip-margin: content-box;\n  position: absolute;\n  right: 0px;\n  stroke: rgb(0, 0, 0);\n  stroke-width: 0px;\n  top: 0px;\n  vector-effect: non-scaling-stroke;\n  width: 30px;\">\n    <g>\n        <defs>\n            <filter id=\"b0f9f707-af61-4336-bcea-c2fee482e562_audioOff-comp-m02dsi55\" height=\"200%\" width=\"200%\" y=\"-50%\" x=\"-50%\">\n                <feOffset result=\"out-offset\" in=\"SourceAlpha\"></feOffset>\n                <feGaussianBlur result=\"out-blur\" in=\"out-offset\" stdDeviation=\"2\"></feGaussianBlur>\n                <feColorMatrix result=\"out-matrix\" in=\"out-blur\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.40 0\"></feColorMatrix>\n                <feMerge>\n                    <feMergeNode in=\"out-matrix\"></feMergeNode>\n                    <feMergeNode in=\"SourceGraphic\"></feMergeNode>\n                </feMerge>\n            </filter>\n        </defs>\n        <path filter=\"url(#b0f9f707-af61-4336-bcea-c2fee482e562_audioOff-comp-m02dsi55)\" d=\"M27.39 17.535a.478.478 0 0 1 .306.615v.001a.51.51 0 0 1-.641.292L6.074 12.471a.478.478 0 0 1-.325-.605.505.505 0 0 1 .661-.302l20.98 5.971zm-6.211.375c1.911-.377 1.812 2.001 1.813 2.001 0 1.273-.986 2.713-3.235 2.713-2.009 0-2.515-1.345-2.515-2.252 0-1.117.646-2.258 2.519-2.258.671-.001 1.095-.141 1.418-.204zm-8.427-1.643v.013h.001l-.005 5.007c0 1.273-.985 2.713-3.233 2.713C7.506 24 7 22.655 7 21.748c0-1.117.646-2.258 2.519-2.258 1.696 0 1.972-.427 1.972-1.319l.001-1.934a.513.513 0 0 1 .512-.477h.23c.285 0 .518.228.518.507zm-.537-4.642a.666.666 0 0 1-.506-.141.61.61 0 0 1-.22-.468l.006-2.86c0-.304.227-.562.535-.609l10.238-1.54a.629.629 0 0 1 .726.609L23 13.591c0 .013-.006.024-.007.036a.49.49 0 0 1-.094.248.516.516 0 0 1-.416.222h-.229a.51.51 0 0 1-.517-.505l-.004-3.479-9.518 1.512z\"></path>\n    </g>\n</svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"bottom": "0px", "cursor": "pointer", "fill": "rgb(255, 255, 255)", "height": "30px", "left": "0px", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "overflowClipMargin": "content-box", "position": "absolute", "right": "0px", "stroke": "rgb(0, 0, 0)", "strokeWidth": "0px", "top": "0px", "vectorEffect": "non-scaling-stroke", "width": "30px"}}, "properties": {"preserveAspectRatio": "xMidYMid meet", "xmlns": "http://www.w3.org/2000/svg", "width": "30", "height": "30", "viewBox": "0 0 30 30", "role": "presentation", "aria-hidden": "true", "aria-label": ""}, "children": []}]}]}]}]}]}]}]}]}