{"blocks": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xop0mqurhj3qu2a", "tagName": "header", "meta": {"naturalWidth": 1520.800048828125}, "responsiveStyles": {"large": {"alignSelf": "stretch", "boxSizing": "border-box", "display": "grid", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "gridTemplate": "1fr / 1fr", "gridTemplateRows": "1fr", "justifySelf": "stretch", "pointerEvents": "none", "position": "relative"}}, "properties": {"slots": "[object Object]"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xp70njbdnvw9ajo", "tagName": "section", "responsiveStyles": {"large": {"alignSelf": "stretch", "backgroundColor": "rgba(252, 252, 255, 0)", "display": "grid", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "gridTemplate": "1fr / minmax(0px, 1fr)", "gridTemplateRows": "1fr", "justifySelf": "stretch", "maxHeight": "99999px", "maxWidth": "99999px", "position": "relative"}}, "properties": {"tabindex": "-1"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xpd0tk2f561pbgl", "tagName": "div", "responsiveStyles": {"large": {"height": "100%", "left": "0px", "mask": "0px 50% / 100% no-repeat", "maskPosition": "0px 50%", "maskRepeat": "no-repeat", "maskSize": "100%", "overflowX": "hidden", "overflowY": "hidden", "position": "absolute", "top": "0px", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xph0dirqjjlv8cg", "tagName": "div", "responsiveStyles": {"large": {"backgroundColor": "rgb(252, 252, 255)", "height": "100%", "position": "absolute", "top": "0px", "transitionProperty": "none", "width": "100%"}}, "properties": {}, "children": []}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xpl06mcvc4ch20j", "tagName": "div", "responsiveStyles": {"large": {"height": "100%"}}, "properties": {}, "children": []}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xpp0agq62r9z8tp", "tagName": "div", "responsiveStyles": {"large": {"display": "grid", "gridTemplate": "1fr / minmax(0px, 1fr)", "gridTemplateRows": "1fr", "overflowX": "clip", "overflowY": "clip", "position": "relative"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xpu0652h054bhzo", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "grid", "gap": "0px", "gridGap": "0px", "gridRowGap": "0px", "gridTemplate": "minmax(41.5898px, auto) / minmax(0px, 1fr)", "gridTemplateRows": "minmax(41.5898px, auto)", "paddingBottom": "2%", "paddingLeft": "2%", "paddingRight": "2%", "paddingTop": "2%", "pointerEvents": "none", "position": "relative", "rowGap": "0px"}}, "properties": {"tabindex": "-1"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xpz0in5d7t0n00k", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "center", "boxSizing": "border-box", "display": "flex", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "justifySelf": "start", "marginBottom": "0%", "marginLeft": "0%", "marginRight": "0%", "marginTop": "0%", "position": "relative", "width": "max-content"}}, "properties": {"role": "", "dir": "ltr"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xq40ctx1zmik576", "tagName": "div", "responsiveStyles": {"large": {"backgroundColor": "rgba(252, 252, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgba(38, 38, 39, 0)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "bottom": "0px", "left": "0px", "position": "absolute", "right": "0px", "top": "0px"}}, "properties": {}, "children": []}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xq80jhjfiz8e0j", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "flex-start", "height": "35px", "marginRight": "11.8828px", "maxHeight": "99999px", "maxWidth": "99999px", "order": "1", "position": "relative", "width": "35px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xqe0o9djxqcy1vf", "tagName": "a", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "bottom": "0px", "color": "rgb(0, 0, 238)", "cursor": "pointer", "left": "0px", "outlineColor": "rgb(0, 0, 238)", "position": "absolute", "right": "0px", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)", "top": "0px"}}, "properties": {"target": "_self"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xqi0j5frpgmsx59", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "bottom": "0px", "color": "rgb(0, 0, 238)", "cursor": "pointer", "left": "0px", "outlineColor": "rgb(0, 0, 238)", "position": "absolute", "right": "0px", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)", "top": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xqm0pm4zfhv1a1", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg preserveAspectRatio=\"xMidYMid meet\" data-bbox=\"30.989 30 138.082 140\" viewBox=\"30.989 30 138.082 140\" height=\"200\" width=\"200\" xmlns=\"http://www.w3.org/2000/svg\" data-type=\"color\" role=\"img\" aria-label=\"Homepage\" style=\"\n  border-color: rgb(0, 0, 238);\n  bottom: 0px;\n  color: rgb(0, 0, 238);\n  cursor: pointer;\n  height: 200px;\n  left: 0px;\n  margin-bottom: auto;\n  margin-left: auto;\n  margin-right: auto;\n  margin-top: auto;\n  outline-color: rgb(0, 0, 238);\n  overflow-clip-margin: content-box;\n  position: absolute;\n  right: 0px;\n  text-decoration: rgb(0, 0, 238);\n  text-decoration-color: rgb(0, 0, 238);\n  text-emphasis-color: rgb(0, 0, 238);\n  top: 0px;\n  vector-effect: non-scaling-stroke;\n  width: 200px;\">\n    <g>\n        <path d=\"M165.538 96.526a3.474 3.474 0 1 1 0 6.947 3.474 3.474 0 0 1 0-6.947M141.11 100a5.705 5.705 0 1 0 11.41 0 5.705 5.705 0 0 0-11.41 0m-20.087 0c0 3.905 3.165 7.07 7.07 7.07s7.07-3.165 7.07-7.07-3.165-7.07-7.07-7.07-7.07 3.165-7.07 7.07m-19.368 0a7.715 7.715 0 1 0 15.43 0 7.715 7.715 0 0 0-15.43 0m-18.729 0c0 4.265 3.457 7.722 7.722 7.722s7.722-3.457 7.722-7.722-3.457-7.722-7.722-7.722-7.722 3.457-7.722 7.722m-3.913 0a7.088 7.088 0 1 0-14.176 0 7.088 7.088 0 0 0 14.176 0m-20.084 0a5.727 5.727 0 1 0-11.454 0 5.727 5.727 0 0 0 11.454 0m-20.958 0a3.491 3.491 0 1 0-6.982 0 3.491 3.491 0 0 0 6.982 0m123.36-16.85a2.895 2.895 0 1 0 5.79 0 2.895 2.895 0 0 0-5.79 0m-20.197 0a4.744 4.744 0 1 0 9.488 0 4.744 4.744 0 0 0-9.488 0m-19.495 0a5.891 5.891 0 1 0 11.782 0 5.891 5.891 0 0 0-11.782 0m-18.886 0a6.43 6.43 0 1 0 12.858 0 6.43 6.43 0 0 0-12.858 0m-18.353 0a6.435 6.435 0 1 0 12.87 0 6.435 6.435 0 0 0-12.87 0m-6.006 0a5.907 5.907 0 1 0-11.815 0 5.907 5.907 0 0 0 11.815 0m-19.492 0a4.763 4.763 0 1 0-9.526 0 4.763 4.763 0 0 0 9.526 0m-20.201 0a2.91 2.91 0 1 0-5.82 0 2.91 2.91 0 0 0 5.82 0m119.063-16.851a2.316 2.316 0 1 0 4.632 0 2.316 2.316 0 0 0-4.632 0m-18.642 0a3.795 3.795 0 1 0 7.59 0 3.795 3.795 0 0 0-7.59 0m-18.081 0a4.713 4.713 0 1 0 9.426 0 4.713 4.713 0 0 0-9.426 0m-17.593 0a5.142 5.142 0 1 0 10.284 0 5.142 5.142 0 0 0-10.284 0m-17.166 0a5.145 5.145 0 1 0 10.29 0 5.145 5.145 0 0 0-10.29 0m-7.293 0a4.725 4.725 0 1 0-9.45 0 4.725 4.725 0 0 0 9.45 0m-18.078 0a3.81 3.81 0 1 0-7.62 0 3.81 3.81 0 0 0 7.62 0m-18.646 0a2.328 2.328 0 1 0-4.655 0 2.328 2.328 0 0 0 4.655 0m100.011-16.85a2.67 2.67 0 1 0 5.338 0 2.67 2.67 0 0 0-5.338 0m-23.805 0a4.006 4.006 0 1 0 8.012 0 4.006 4.006 0 0 0-8.012 0m-22.88 0a4.418 4.418 0 1 0 8.836 0 4.418 4.418 0 0 0-8.836 0m-14.035 0a4.016 4.016 0 1 0-8.032 0 4.016 4.016 0 0 0 8.032 0m-23.803 0a2.681 2.681 0 1 0-5.362 0 2.681 2.681 0 0 0 5.362 0m74.389-16.85a1.57 1.57 0 1 0 3.14 0 1.57 1.57 0 0 0-3.14 0m-17.638 0a2.356 2.356 0 1 0 4.712 0 2.356 2.356 0 0 0-4.712 0m-11.897 0a2.599 2.599 0 1 0-5.198 0 2.599 2.599 0 0 0 5.198 0m-17.088 0a2.362 2.362 0 1 0-4.724 0 2.362 2.362 0 0 0 4.724 0m-17.637 0a1.577 1.577 0 1 0-3.154 0 1.577 1.577 0 0 0 3.154 0M38.687 116.85a2.895 2.895 0 1 0-5.79 0 2.895 2.895 0 0 0 5.79 0m20.196 0a4.744 4.744 0 1 0-9.488 0 4.744 4.744 0 0 0 9.488 0m19.495 0a5.891 5.891 0 1 0-11.782 0 5.891 5.891 0 0 0 11.782 0m18.886 0a6.43 6.43 0 1 0-12.858 0 6.43 6.43 0 0 0 12.858 0m5.484 0a6.435 6.435 0 1 0 12.87 0 6.435 6.435 0 0 0-12.87 0m18.875 0a5.907 5.907 0 1 0 11.815 0 5.907 5.907 0 0 0-11.815 0m19.492 0a4.763 4.763 0 1 0 9.526 0 4.763 4.763 0 0 0-9.526 0m20.201 0a2.91 2.91 0 1 0 5.82 0 2.91 2.91 0 0 0-5.82 0M42.253 133.701a2.316 2.316 0 1 0-4.632 0 2.316 2.316 0 0 0 4.632 0m18.643 0a3.795 3.795 0 1 0-7.59 0 3.795 3.795 0 0 0 7.59 0m18.081 0a4.713 4.713 0 1 0-9.426 0 4.713 4.713 0 0 0 9.426 0m17.593 0a5.142 5.142 0 1 0-10.284 0 5.142 5.142 0 0 0 10.284 0m6.875 0a5.145 5.145 0 1 0 10.29 0 5.145 5.145 0 0 0-10.29 0m17.584 0a4.724 4.724 0 1 0 9.45 0 4.725 4.725 0 0 0-9.45 0m18.078 0a3.81 3.81 0 1 0 7.62 0 3.81 3.81 0 0 0-7.62 0m18.645 0a2.328 2.328 0 1 0 4.655 0 2.328 2.328 0 0 0-4.655 0m-100.011 16.85a2.67 2.67 0 1 0-5.338 0 2.67 2.67 0 0 0 5.338 0m23.805 0a4.006 4.006 0 1 0-8.012 0 4.006 4.006 0 0 0 8.012 0m14.045 0a4.418 4.418 0 1 0 8.836 0 4.418 4.418 0 0 0-8.836 0m22.87 0a4.016 4.016 0 1 0 8.032 0 4.016 4.016 0 0 0-8.032 0m23.804 0a2.68 2.68 0 1 0 5.361 0 2.68 2.68 0 0 0-5.361 0m-74.39 16.85a1.57 1.57 0 1 0-3.14 0 1.57 1.57 0 0 0 3.14 0m17.638 0a2.356 2.356 0 1 0-4.712 0 2.356 2.356 0 0 0 4.712 0m11.897 0a2.599 2.599 0 1 0 5.198 0 2.599 2.599 0 0 0-5.198 0m17.088 0a2.362 2.362 0 1 0 4.724 0 2.362 2.362 0 0 0-4.724 0m17.638 0a1.577 1.577 0 1 0 3.154 0 1.577 1.577 0 0 0-3.154 0\" fill=\"#282828\" data-color=\"1\"></path>\n    </g>\n</svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "bottom": "0px", "color": "rgb(0, 0, 238)", "cursor": "pointer", "height": "200px", "left": "0px", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "outlineColor": "rgb(0, 0, 238)", "overflowClipMargin": "content-box", "position": "absolute", "right": "0px", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)", "top": "0px", "vectorEffect": "non-scaling-stroke", "width": "200px"}}, "properties": {"preserveAspectRatio": "xMidYMid meet", "viewBox": "30.989 30 138.082 140", "height": "200", "width": "200", "xmlns": "http://www.w3.org/2000/svg", "role": "img", "aria-label": "Homepage"}, "children": []}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xqs0ahbq8lk6k19", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "flex-start", "marginTop": "8.29934px", "maxHeight": "99999px", "maxWidth": "99999px", "order": "2", "overflowWrap": "break-word", "position": "relative", "textShadow": "rgba(0, 0, 0, 0) 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px", "width": "93px", "wordWrap": "break-word"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xqw0i0t7olrevi", "tagName": "p", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "color": "rgb(38, 38, 39)", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "18px", "fontWeight": "700", "letterSpacing": "0.36px", "lineHeight": "21.6px", "outlineColor": "rgb(38, 38, 39)", "overflowWrap": "break-word", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textShadow": "rgba(0, 0, 0, 0) 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px", "wordWrap": "break-word"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xr10k2nnkxr3eb", "tagName": "a", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "color": "rgb(38, 38, 39)", "cursor": "pointer", "display": "inline", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "18px", "fontWeight": "700", "letterSpacing": "0.36px", "lineHeight": "21.6px", "outlineColor": "rgb(38, 38, 39)", "overflowWrap": "break-word", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textShadow": "rgba(0, 0, 0, 0) 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px", "wordWrap": "break-word"}}, "properties": {"target": "_self"}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "ARRAY AI"}}}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xr50xz1b59i452s", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "center", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "justifySelf": "end", "marginBottom": "0.0012797%", "marginLeft": "0%", "marginRight": "-0.00228475%", "marginTop": "0%", "maxHeight": "99999px", "maxWidth": "99999px", "minHeight": "29.3906px", "position": "relative", "width": "87.9491%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xra0bnpmqnz6fkf", "tagName": "nav", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "display": "flex", "height": "100%", "overflowX": "scroll", "overflowY": "auto", "scrollbarWidth": "none", "width": "100%"}}, "properties": {"aria-label": "Site"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xre0x4t80ut28z", "tagName": "ul", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "flex", "flexGrow": "1", "justifyContent": "flex-end", "minWidth": "fit-content", "width": "calc(100% + 8px)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xrk0ws013q8py9", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "list-item", "flexGrow": "1", "textAlign": "left"}}, "properties": {"aria-current": "true"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xro0vl7cnruo5lg", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xrr0brcl3bzvk5", "tagName": "a", "responsiveStyles": {"large": {"backgroundAttachment": "scroll, scroll", "backgroundClip": "border-box, border-box", "backgroundColor": "rgba(117, 117, 117, 0)", "backgroundImage": "linear-gradient(rgb(252, 252, 255), rgb(252, 252, 255)), none", "backgroundOrigin": "padding-box, padding-box", "backgroundPosition": "0% 50%, 0% 0%", "backgroundPositionX": "0%, 0%", "backgroundPositionY": "50%, 0%", "backgroundRepeat": "repeat, repeat", "backgroundSize": "auto, auto", "borderBottomStyle": "solid", "borderColor": "rgb(92, 91, 94) rgb(92, 91, 94) rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(92, 91, 94)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRadius": "4px 5px 0px 0px", "borderRight": "0.8px solid rgb(92, 91, 94)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(92, 91, 94)", "borderTopLeftRadius": "4px", "borderTopRightRadius": "5px", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px 0.8px 0px", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "flexBasis": "0%", "flexGrow": "1", "height": "100%", "marginRight": "4px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "7px", "paddingLeft": "12px", "paddingRight": "12px", "paddingTop": "7px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)", "transitionDuration": "0.08s", "transitionTimingFunction": "linear"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xrw08t0fw9udstx", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "height": "100%", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xs00roxcro1pnf", "tagName": "span", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "pointer", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "12px", "letterSpacing": "1.2px", "lineHeight": "12.72px", "textAlign": "left", "textTransform": "uppercase", "textWrap": "nowrap", "transitionDuration": "0.08s", "transitionTimingFunction": "linear", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Home"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xs407yzyo01lqj7", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "list-item", "flexGrow": "1", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xs80w5ojfolikv", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "flex", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xsb0wfanprdla0l", "tagName": "a", "responsiveStyles": {"large": {"backgroundAttachment": "scroll, scroll", "backgroundClip": "border-box, border-box", "backgroundColor": "rgba(117, 117, 117, 0)", "backgroundImage": "linear-gradient(rgb(252, 252, 255), rgb(252, 252, 255)), none", "backgroundOrigin": "padding-box, padding-box", "backgroundPosition": "0% 50%, 0% 0%", "backgroundPositionX": "0%, 0%", "backgroundPositionY": "50%, 0%", "backgroundRepeat": "repeat, repeat", "backgroundSize": "auto, auto", "borderBottomStyle": "solid", "borderColor": "rgb(92, 91, 94) rgb(92, 91, 94) rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(92, 91, 94)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRadius": "4px 5px 0px 0px", "borderRight": "0.8px solid rgb(92, 91, 94)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(92, 91, 94)", "borderTopLeftRadius": "4px", "borderTopRightRadius": "5px", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px 0.8px 0px", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "flexBasis": "0%", "flexGrow": "1", "height": "100%", "marginLeft": "4px", "marginRight": "4px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "7px", "paddingLeft": "12px", "paddingRight": "12px", "paddingTop": "7px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "aria-expanded": "false", "aria-haspopup": "true", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xsg00lpcxilsw1nl", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "height": "100%", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xsk0d04e7sbxf56", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(92, 91, 94)", "boxSizing": "border-box", "color": "rgb(92, 91, 94)", "cursor": "pointer", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "12px", "letterSpacing": "1.2px", "lineHeight": "12.72px", "outlineColor": "rgb(92, 91, 94)", "textAlign": "left", "textDecoration": "rgb(92, 91, 94)", "textDecorationColor": "rgb(92, 91, 94)", "textEmphasisColor": "rgb(92, 91, 94)", "textTransform": "uppercase", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "AI Solutions"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xso0dj6ikisu9eq", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left", "width": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xss0zkawt0g6kwg", "tagName": "button", "responsiveStyles": {"large": {"appearance": "auto", "boxSizing": "border-box", "clip": "rect(0px, 0px, 0px, 0px)", "clipPath": "inset(50%)", "cursor": "default", "display": "inline-block", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "textAlign": "center", "width": "0px", "backgroundColor": "rgba(0, 0, 0, 0)", "borderColor": "rgba(0, 0, 0, 0)"}, "medium": {"borderColor": "rgb(0, 0, 0)"}}, "properties": {"tabindex": "0", "aria-label": "Toggle AI Solutions"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xsx0cjo1rqpkcyd", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg width=\"0\" height=\"0\" viewBox=\"0 0 0 0\" fill=\"black\" xmlns=\"http://www.w3.org/2000/svg\" style=\"\n  box-sizing: border-box;\n  cursor: default;\n  display: inline;\n  font-family: Arial;\n  font-size: 13.3333px;\n  height: 0px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  text-align: center;\n  width: 0px;\"><path d=\"M8 10.5L16 1.86193L14.7387 0.5L8 7.77613L1.26133 0.499999L-5.95321e-08 1.86193L8 10.5Z\"></path></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "default", "display": "inline", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "center", "width": "0px"}}, "properties": {"width": "0", "height": "0", "viewBox": "0 0 0 0", "fill": "black", "xmlns": "http://www.w3.org/2000/svg"}, "children": []}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xt10f1f6dfkpd6d", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "none", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "position": "fixed", "textAlign": "left", "zIndex": "47"}}, "properties": {"role": "group", "aria-label": "AI Solutions"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xt5021j7j828wmk", "tagName": "div", "responsiveStyles": {"large": {"animation": "0.1s ease 0.05s 1 normal forwards running none", "animationDelay": "0.05s", "animationDuration": "0.1s", "animationFillMode": "forwards", "backgroundColor": "rgb(252, 252, 255)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "opacity": "0", "paddingBottom": "10px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "10px", "textAlign": "left", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.08s, 0.08s", "transitionProperty": "border-color, box-shadow", "transitionTimingFunction": "cubic-bezier(0.25, 1, 0.5, 1), cubic-bezier(0.25, 1, 0.5, 1)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xta0bpfwvkksn0t", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "justifyContent": "space-between", "marginBottom": "-8px", "marginLeft": "auto", "marginRight": "auto", "textAlign": "left", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xte0oh5bl7lr7n9", "tagName": "ul", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xth0z2xsh5pddg9", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xtl0k2yycv10c6j", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xtp0ms7jhnm7hs", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xtt0vswnwl5ygc", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xtw0d9gc4qhp786", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Agentic AI"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xu10q1hsysnv4uq", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xu40swzk2ot0xng", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xu90601kvcuf78p", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xuc0egfyg4nyqra", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xug0pp0nx6juhr", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Vibe Coding"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xuk0baxj4frlhn6", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xuo0zx1ppd0lxmc", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xus0mni7gisjys", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xvd0urdl6sob38", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xvh0ljg42jnzbf9", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "AI Chatbots"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xvl00sgi94d99e2", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xvq00qyyjluljfuf", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xvu0rk9i69ton4o", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xvx0wzdxvdzt7g", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xw10nquguw3pm2f", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Knowledge Graphs"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xw5075gfmaqk8st", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xw80rxeiaqtde7c", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xwc0d2vkfcilwpa", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xwg016w60ifrn91", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xwj0wtfd9strf4", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Automation Solutions"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xwn0gs9xeke5h1", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xwr0d9a4t0q24e9", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xwv0i6wz1gpivp", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xwz01aetnpvailg", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xx40o0a6w46zgw", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "RPA Automation"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xx805j8sy0xlyh", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xxc0118dxl0167o", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xxf07as4bvwx7ma", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xxk02g6dbj4ey93", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xxo0u0x5lt7xis", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Document Intelligence"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xxt06wex3u5e6c", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xxx01u1t4ojhgux", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xy203kc8tsi3405", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xy80vblee8cxiwr", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xyf0zd8hklewmas", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Custom RAG Development"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}]}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xym0udghxfjuvj", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "list-item", "flexGrow": "1", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xyt0gwfu1ff4l2b", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "flex", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xyz031yrhha0q4r", "tagName": "a", "responsiveStyles": {"large": {"backgroundAttachment": "scroll, scroll", "backgroundClip": "border-box, border-box", "backgroundColor": "rgba(117, 117, 117, 0)", "backgroundImage": "linear-gradient(rgb(252, 252, 255), rgb(252, 252, 255)), none", "backgroundOrigin": "padding-box, padding-box", "backgroundPosition": "0% 50%, 0% 0%", "backgroundPositionX": "0%, 0%", "backgroundPositionY": "50%, 0%", "backgroundRepeat": "repeat, repeat", "backgroundSize": "auto, auto", "borderBottomStyle": "solid", "borderColor": "rgb(92, 91, 94) rgb(92, 91, 94) rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(92, 91, 94)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRadius": "4px 5px 0px 0px", "borderRight": "0.8px solid rgb(92, 91, 94)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(92, 91, 94)", "borderTopLeftRadius": "4px", "borderTopRightRadius": "5px", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px 0.8px 0px", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "flexBasis": "0%", "flexGrow": "1", "height": "100%", "marginLeft": "4px", "marginRight": "4px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "7px", "paddingLeft": "12px", "paddingRight": "12px", "paddingTop": "7px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "aria-expanded": "false", "aria-haspopup": "true", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xz60gzt0idyqga7", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "height": "100%", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xzc02a2a1vmwz7m", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(92, 91, 94)", "boxSizing": "border-box", "color": "rgb(92, 91, 94)", "cursor": "pointer", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "12px", "letterSpacing": "1.2px", "lineHeight": "12.72px", "outlineColor": "rgb(92, 91, 94)", "textAlign": "left", "textDecoration": "rgb(92, 91, 94)", "textDecorationColor": "rgb(92, 91, 94)", "textEmphasisColor": "rgb(92, 91, 94)", "textTransform": "uppercase", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Development"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xzj06aeym0hn9de", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left", "width": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xzn0l3xmr0vwhaf", "tagName": "button", "responsiveStyles": {"large": {"appearance": "auto", "boxSizing": "border-box", "clip": "rect(0px, 0px, 0px, 0px)", "clipPath": "inset(50%)", "cursor": "default", "display": "inline-block", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "textAlign": "center", "width": "0px", "backgroundColor": "rgba(0, 0, 0, 0)", "borderColor": "rgba(0, 0, 0, 0)"}, "medium": {"borderColor": "rgb(0, 0, 0)"}}, "properties": {"tabindex": "0", "aria-label": "Toggle Development"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xzr0fmlebrsmjep", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg width=\"0\" height=\"0\" viewBox=\"0 0 0 0\" fill=\"black\" xmlns=\"http://www.w3.org/2000/svg\" style=\"\n  box-sizing: border-box;\n  cursor: default;\n  display: inline;\n  font-family: Arial;\n  font-size: 13.3333px;\n  height: 0px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  text-align: center;\n  width: 0px;\"><path d=\"M8 10.5L16 1.86193L14.7387 0.5L8 7.77613L1.26133 0.499999L-5.95321e-08 1.86193L8 10.5Z\"></path></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "default", "display": "inline", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "center", "width": "0px"}}, "properties": {"width": "0", "height": "0", "viewBox": "0 0 0 0", "fill": "black", "xmlns": "http://www.w3.org/2000/svg"}, "children": []}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xzu0wi88kvz7e7g", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "none", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "position": "fixed", "textAlign": "left", "zIndex": "47"}}, "properties": {"role": "group", "aria-label": "Development"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86xzy0yv97ccb9cad", "tagName": "div", "responsiveStyles": {"large": {"animation": "0.1s ease 0.05s 1 normal forwards running none", "animationDelay": "0.05s", "animationDuration": "0.1s", "animationFillMode": "forwards", "backgroundColor": "rgb(252, 252, 255)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "opacity": "0", "paddingBottom": "10px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "10px", "textAlign": "left", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.08s, 0.08s", "transitionProperty": "border-color, box-shadow", "transitionTimingFunction": "cubic-bezier(0.25, 1, 0.5, 1), cubic-bezier(0.25, 1, 0.5, 1)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y020vohsa4iil19", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "justifyContent": "space-between", "marginBottom": "-8px", "marginLeft": "auto", "marginRight": "auto", "textAlign": "left", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y0605xy7s9gw3o5", "tagName": "ul", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y0a0nf6il4zyeqc", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y0d0bj282w7u4w6", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y0h06zjp1r9ymwx", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y0k0i2q9kcy2fo", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y0p0ugg2l7xyxh", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Website Development"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y0t0o75bde0ye0m", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y0z036xvhlztaxi", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y16088r62p4jx7f", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y1c0g7cbd14ks1a", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y1j0rku3yud2uy8", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Web App Development"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y1q0p6ohq1xw81j", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y1w0ghle9x180uq", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y220kmtio0vkilb", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y290upfcoj2jsxd", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y2d0bwi4kswz5zp", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Android App Development"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y2j0jda9navmwn", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y2m0bn87pkaej3o", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y2q0if33bkktcz", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y2u0s9ay1hvarlj", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y2y071wh89zf3za", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "IOS App Development"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y320dnl1a8pbk1", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y350jfe2w0r8z6", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y39079nhfma0yng", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y3d0cgnarxys8qf", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y3g0oktkq1ktysp", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Custom Software Development"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}]}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y3l07dudqs1ub3", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "list-item", "flexGrow": "1", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y3r0sqi7mtjmtw", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "flex", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y3x0mihfz705qml", "tagName": "a", "responsiveStyles": {"large": {"backgroundAttachment": "scroll, scroll", "backgroundClip": "border-box, border-box", "backgroundColor": "rgba(117, 117, 117, 0)", "backgroundImage": "linear-gradient(rgb(252, 252, 255), rgb(252, 252, 255)), none", "backgroundOrigin": "padding-box, padding-box", "backgroundPosition": "0% 50%, 0% 0%", "backgroundPositionX": "0%, 0%", "backgroundPositionY": "50%, 0%", "backgroundRepeat": "repeat, repeat", "backgroundSize": "auto, auto", "borderBottomStyle": "solid", "borderColor": "rgb(92, 91, 94) rgb(92, 91, 94) rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(92, 91, 94)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRadius": "4px 5px 0px 0px", "borderRight": "0.8px solid rgb(92, 91, 94)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(92, 91, 94)", "borderTopLeftRadius": "4px", "borderTopRightRadius": "5px", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px 0.8px 0px", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "flexBasis": "0%", "flexGrow": "1", "height": "100%", "marginLeft": "4px", "marginRight": "4px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "7px", "paddingLeft": "12px", "paddingRight": "12px", "paddingTop": "7px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "aria-expanded": "false", "aria-haspopup": "true", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y440yyq2fg2t4ub", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "height": "100%", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y4a08pjt05m42s", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(92, 91, 94)", "boxSizing": "border-box", "color": "rgb(92, 91, 94)", "cursor": "pointer", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "12px", "letterSpacing": "1.2px", "lineHeight": "12.72px", "outlineColor": "rgb(92, 91, 94)", "textAlign": "left", "textDecoration": "rgb(92, 91, 94)", "textDecorationColor": "rgb(92, 91, 94)", "textEmphasisColor": "rgb(92, 91, 94)", "textTransform": "uppercase", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "SAP"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y4g010riw3avd5o", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left", "width": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y4n0bv9d0w0c1pb", "tagName": "button", "responsiveStyles": {"large": {"appearance": "auto", "boxSizing": "border-box", "clip": "rect(0px, 0px, 0px, 0px)", "clipPath": "inset(50%)", "cursor": "default", "display": "inline-block", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "textAlign": "center", "width": "0px", "backgroundColor": "rgba(0, 0, 0, 0)", "borderColor": "rgba(0, 0, 0, 0)"}, "medium": {"borderColor": "rgb(0, 0, 0)"}}, "properties": {"tabindex": "0", "aria-label": "Toggle SAP"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y4t0k6h935o26hf", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg width=\"0\" height=\"0\" viewBox=\"0 0 0 0\" fill=\"black\" xmlns=\"http://www.w3.org/2000/svg\" style=\"\n  box-sizing: border-box;\n  cursor: default;\n  display: inline;\n  font-family: Arial;\n  font-size: 13.3333px;\n  height: 0px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  text-align: center;\n  width: 0px;\"><path d=\"M8 10.5L16 1.86193L14.7387 0.5L8 7.77613L1.26133 0.499999L-5.95321e-08 1.86193L8 10.5Z\"></path></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "default", "display": "inline", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "center", "width": "0px"}}, "properties": {"width": "0", "height": "0", "viewBox": "0 0 0 0", "fill": "black", "xmlns": "http://www.w3.org/2000/svg"}, "children": []}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y4z0lg9wb4a2ved", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "none", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "position": "fixed", "textAlign": "left", "zIndex": "47"}}, "properties": {"role": "group", "aria-label": "SAP"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y5404ivuqifkvy4", "tagName": "div", "responsiveStyles": {"large": {"animation": "0.1s ease 0.05s 1 normal forwards running none", "animationDelay": "0.05s", "animationDuration": "0.1s", "animationFillMode": "forwards", "backgroundColor": "rgb(252, 252, 255)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "opacity": "0", "paddingBottom": "10px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "10px", "textAlign": "left", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.08s, 0.08s", "transitionProperty": "border-color, box-shadow", "transitionTimingFunction": "cubic-bezier(0.25, 1, 0.5, 1), cubic-bezier(0.25, 1, 0.5, 1)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y570hxc82f7h5oe", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "justifyContent": "space-between", "marginBottom": "-8px", "marginLeft": "auto", "marginRight": "auto", "textAlign": "left", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y5b0fyerv8ym16v", "tagName": "ul", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y5f0d64n42ksx3t", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y5i0jbibkoovt5a", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y5m0kbo6yj0p4i", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y5p02wir8on6tj9", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y5t0iceo6onxvs", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "SAP Business One"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y5x05cf3sx4qevr", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y610h2xxngpzf25", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y640f4qew6oopz", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y6805v2qkykipu7", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y6c0yatuq24q0na", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "SAP Business One Cloud"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y6i0kxcn987zlu", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y6o0cqmhtzto1lm", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y6v0rl0pxm4rnf", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y7104y9ex1pbqgo", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y770c6fu7vnwsfq", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "SAP S/4HANA"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y7e0admf6k7uv59", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y7l0out2i3hkj5s", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y7r0n4ya816r2ej", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y7x0sq97db36izd", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y81001rzvorp5l7f", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "SAP HANA"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y850mykzxdod8nm", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y8808r333bvu7g", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y8c0wbtk31i5f18", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y8f0667hlfpelmf", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y8j0l4cd31ii3gp", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Beas Manufacturing"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y8n0mthzfmc3tft", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y8r0fvxxof61lk", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y8u0hd5wjw64z5", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y8y04j3efmev7wh", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y920s5sytl7fau", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "ERP Planning"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}]}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y9705mqyiib7t9a", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "list-item", "flexGrow": "1", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y9d01ksc6w2c24h", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "flex", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y9j0i3m67odahje", "tagName": "a", "responsiveStyles": {"large": {"backgroundAttachment": "scroll, scroll", "backgroundClip": "border-box, border-box", "backgroundColor": "rgba(117, 117, 117, 0)", "backgroundImage": "linear-gradient(rgb(252, 252, 255), rgb(252, 252, 255)), none", "backgroundOrigin": "padding-box, padding-box", "backgroundPosition": "0% 50%, 0% 0%", "backgroundPositionX": "0%, 0%", "backgroundPositionY": "50%, 0%", "backgroundRepeat": "repeat, repeat", "backgroundSize": "auto, auto", "borderBottomStyle": "solid", "borderColor": "rgb(92, 91, 94) rgb(92, 91, 94) rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(92, 91, 94)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRadius": "4px 5px 0px 0px", "borderRight": "0.8px solid rgb(92, 91, 94)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(92, 91, 94)", "borderTopLeftRadius": "4px", "borderTopRightRadius": "5px", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px 0.8px 0px", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "flexBasis": "0%", "flexGrow": "1", "height": "100%", "marginLeft": "4px", "marginRight": "4px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "7px", "paddingLeft": "12px", "paddingRight": "12px", "paddingTop": "7px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "aria-expanded": "false", "aria-haspopup": "true", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y9q0ed0n4iejxrf", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "height": "100%", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86y9w0u7a4jpstgac", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(92, 91, 94)", "boxSizing": "border-box", "color": "rgb(92, 91, 94)", "cursor": "pointer", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "12px", "letterSpacing": "1.2px", "lineHeight": "12.72px", "outlineColor": "rgb(92, 91, 94)", "textAlign": "left", "textDecoration": "rgb(92, 91, 94)", "textDecorationColor": "rgb(92, 91, 94)", "textEmphasisColor": "rgb(92, 91, 94)", "textTransform": "uppercase", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Marketing"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ya20tyv636np6c", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left", "width": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ya80hzjc7xuoy37", "tagName": "button", "responsiveStyles": {"large": {"appearance": "auto", "boxSizing": "border-box", "clip": "rect(0px, 0px, 0px, 0px)", "clipPath": "inset(50%)", "cursor": "default", "display": "inline-block", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "textAlign": "center", "width": "0px", "backgroundColor": "rgba(0, 0, 0, 0)", "borderColor": "rgba(0, 0, 0, 0)"}, "medium": {"borderColor": "rgb(0, 0, 0)"}}, "properties": {"tabindex": "0", "aria-label": "Toggle Marketing"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yae0fjkzpms9ppo", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg width=\"0\" height=\"0\" viewBox=\"0 0 0 0\" fill=\"black\" xmlns=\"http://www.w3.org/2000/svg\" style=\"\n  box-sizing: border-box;\n  cursor: default;\n  display: inline;\n  font-family: Arial;\n  font-size: 13.3333px;\n  height: 0px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  text-align: center;\n  width: 0px;\"><path d=\"M8 10.5L16 1.86193L14.7387 0.5L8 7.77613L1.26133 0.499999L-5.95321e-08 1.86193L8 10.5Z\"></path></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "default", "display": "inline", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "center", "width": "0px"}}, "properties": {"width": "0", "height": "0", "viewBox": "0 0 0 0", "fill": "black", "xmlns": "http://www.w3.org/2000/svg"}, "children": []}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yaj06ns252rotqc", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "none", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "position": "fixed", "textAlign": "left", "zIndex": "47"}}, "properties": {"role": "group", "aria-label": "Marketing"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yao0itrbgalmlz", "tagName": "div", "responsiveStyles": {"large": {"animation": "0.1s ease 0.05s 1 normal forwards running none", "animationDelay": "0.05s", "animationDuration": "0.1s", "animationFillMode": "forwards", "backgroundColor": "rgb(252, 252, 255)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "opacity": "0", "paddingBottom": "10px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "10px", "textAlign": "left", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.08s, 0.08s", "transitionProperty": "border-color, box-shadow", "transitionTimingFunction": "cubic-bezier(0.25, 1, 0.5, 1), cubic-bezier(0.25, 1, 0.5, 1)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yar0hovj3vr48ip", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "justifyContent": "space-between", "marginBottom": "-8px", "marginLeft": "auto", "marginRight": "auto", "textAlign": "left", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yav0akqih1be2kb", "tagName": "ul", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yaz0mkjy1ty7e5f", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yb20ql93plxvsbb", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yb60if1wbshzv9", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yba0us1iu3ep5or", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ybd00ae89k3b3zp9", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Business Growth"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ybi0y4fw2wtcmtl", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ybl09xmwibxvp2v", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ybp0zpzdm5fhjdg", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ybt0lhbq7ty6yws", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ybx0w1604c81vtc", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Digital Marketing"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yc40ybk3k8vc3em", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yca0g5uosexg84a", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ycg0bpjlclqq6g", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ycn09k1rds8vmpl", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yct0093kwxch5ran", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Performance Marketing"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yd00l4hz0blsnma", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yd605unwa5hijr4", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ydd04jsiqt7ubny", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ydh0rhcyc0ggjvr", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ydl09g0xy8jk7v6", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Content Creation"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ydp02n3mixl0siq", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ydt0fbepno0goi9", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ydw063b1mtcmsqa", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ye000rp04tqsn0ke", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ye403nnuamlr2zu", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Video Editing"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ye80fa4jtpdrxir", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yeb0knl9eezgy38", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yef0kw6vkywor5b", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yej0ye71w91l60m", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yem0qco6gzbrml", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "SEO"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yes0g6egyln6iq9", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yey0k41assprrb", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yf50fohkfynu566", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yfb0s3iulq488b", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yfi0ulcrkiy93v", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "SEM/PPC"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yfo0johkc0nvr2", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yfv0yk4ji78udbg", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yg10s13xzlo2bv", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ygb0cqr9nrgkzc", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ygt0op9w48d0ay", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Custom Campaigns"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}]}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yh201fsppog6bxy", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "list-item", "flexGrow": "1", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yh90vwsxaloiabs", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "flex", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yhd0bjo758wrsus", "tagName": "a", "responsiveStyles": {"large": {"backgroundAttachment": "scroll, scroll", "backgroundClip": "border-box, border-box", "backgroundColor": "rgba(117, 117, 117, 0)", "backgroundImage": "linear-gradient(rgb(252, 252, 255), rgb(252, 252, 255)), none", "backgroundOrigin": "padding-box, padding-box", "backgroundPosition": "0% 50%, 0% 0%", "backgroundPositionX": "0%, 0%", "backgroundPositionY": "50%, 0%", "backgroundRepeat": "repeat, repeat", "backgroundSize": "auto, auto", "borderBottomStyle": "solid", "borderColor": "rgb(92, 91, 94) rgb(92, 91, 94) rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(92, 91, 94)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRadius": "4px 5px 0px 0px", "borderRight": "0.8px solid rgb(92, 91, 94)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(92, 91, 94)", "borderTopLeftRadius": "4px", "borderTopRightRadius": "5px", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px 0.8px 0px", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "flexBasis": "0%", "flexGrow": "1", "height": "100%", "marginLeft": "4px", "marginRight": "4px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "7px", "paddingLeft": "12px", "paddingRight": "12px", "paddingTop": "7px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "aria-expanded": "false", "aria-haspopup": "true", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yhh0igxqebg6wd", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "height": "100%", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yhn07kspxnmc8nn", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(92, 91, 94)", "boxSizing": "border-box", "color": "rgb(92, 91, 94)", "cursor": "pointer", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "12px", "letterSpacing": "1.2px", "lineHeight": "12.72px", "outlineColor": "rgb(92, 91, 94)", "textAlign": "left", "textDecoration": "rgb(92, 91, 94)", "textDecorationColor": "rgb(92, 91, 94)", "textEmphasisColor": "rgb(92, 91, 94)", "textTransform": "uppercase", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Resources"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2/resources"}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yhu050gub7nbssj", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left", "width": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yi005j8l2jwgrs8", "tagName": "button", "responsiveStyles": {"large": {"appearance": "auto", "boxSizing": "border-box", "clip": "rect(0px, 0px, 0px, 0px)", "clipPath": "inset(50%)", "cursor": "default", "display": "inline-block", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "textAlign": "center", "width": "0px", "backgroundColor": "rgba(0, 0, 0, 0)", "borderColor": "rgba(0, 0, 0, 0)"}, "medium": {"borderColor": "rgb(0, 0, 0)"}}, "properties": {"tabindex": "0", "aria-label": "Toggle Resources"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yi60cb2jiianqcg", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg width=\"0\" height=\"0\" viewBox=\"0 0 0 0\" fill=\"black\" xmlns=\"http://www.w3.org/2000/svg\" style=\"\n  box-sizing: border-box;\n  cursor: default;\n  display: inline;\n  font-family: Arial;\n  font-size: 13.3333px;\n  height: 0px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  text-align: center;\n  width: 0px;\"><path d=\"M8 10.5L16 1.86193L14.7387 0.5L8 7.77613L1.26133 0.499999L-5.95321e-08 1.86193L8 10.5Z\"></path></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "default", "display": "inline", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "0px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "center", "width": "0px"}}, "properties": {"width": "0", "height": "0", "viewBox": "0 0 0 0", "fill": "black", "xmlns": "http://www.w3.org/2000/svg"}, "children": []}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yic0lleiqgby2k", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "none", "marginBottom": "auto", "marginLeft": "auto", "marginRight": "auto", "marginTop": "auto", "position": "fixed", "textAlign": "left", "zIndex": "47"}}, "properties": {"role": "group", "aria-label": "Resources"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yii01gu1kzty057", "tagName": "div", "responsiveStyles": {"large": {"animation": "0.1s ease 0.05s 1 normal forwards running none", "animationDelay": "0.05s", "animationDuration": "0.1s", "animationFillMode": "forwards", "backgroundColor": "rgb(252, 252, 255)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "opacity": "0", "paddingBottom": "10px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "10px", "textAlign": "left", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.08s, 0.08s", "transitionProperty": "border-color, box-shadow", "transitionTimingFunction": "cubic-bezier(0.25, 1, 0.5, 1), cubic-bezier(0.25, 1, 0.5, 1)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yio0qnu6246b318", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "justifyContent": "space-between", "marginBottom": "-8px", "marginLeft": "auto", "marginRight": "auto", "textAlign": "left", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yiv0d73j8wpqb5o", "tagName": "ul", "responsiveStyles": {"large": {"boxSizing": "border-box", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yj00nxhapidpqxe", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yj40k9en2q1jrxa", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yj804uj9jicie5r", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yjc0zubk5ptm9is", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yjg0o6ub0sswqn", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Blog"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2/resources"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yjk03yruyhvoxl8", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "breakInside": "avoid", "display": "list-item", "pageBreakInside": "avoid", "position": "relative", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yjn060hb5ns7kaf", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yjr0td9n7uyujs", "tagName": "a", "responsiveStyles": {"large": {"backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "marginBottom": "8px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "8px", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "8px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yjv0ridb3h51vwc", "tagName": "div", "responsiveStyles": {"large": {"borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yjz0gjh9pk3hij", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(38, 38, 39)", "boxSizing": "border-box", "color": "rgb(38, 38, 39)", "cursor": "pointer", "fontFamily": "helvetica-w01-roman, sans-serif", "fontSize": "18px", "letterSpacing": "0.9px", "outlineColor": "rgb(38, 38, 39)", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "left", "textDecoration": "rgb(38, 38, 39)", "textDecorationColor": "rgb(38, 38, 39)", "textEmphasisColor": "rgb(38, 38, 39)", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "FAQ"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2/resources"}]}]}]}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yk40704f8oo2vor", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "list-item", "flexGrow": "1", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yk80qwuw04fl5gg", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ykd0sldgbhzplt", "tagName": "a", "responsiveStyles": {"large": {"backgroundAttachment": "scroll, scroll", "backgroundClip": "border-box, border-box", "backgroundColor": "rgba(117, 117, 117, 0)", "backgroundImage": "linear-gradient(rgb(252, 252, 255), rgb(252, 252, 255)), none", "backgroundOrigin": "padding-box, padding-box", "backgroundPosition": "0% 50%, 0% 0%", "backgroundPositionX": "0%, 0%", "backgroundPositionY": "50%, 0%", "backgroundRepeat": "repeat, repeat", "backgroundSize": "auto, auto", "borderBottomStyle": "solid", "borderColor": "rgb(92, 91, 94) rgb(92, 91, 94) rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(92, 91, 94)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRadius": "4px 5px 0px 0px", "borderRight": "0.8px solid rgb(92, 91, 94)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(92, 91, 94)", "borderTopLeftRadius": "4px", "borderTopRightRadius": "5px", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px 0.8px 0px", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "flexBasis": "0%", "flexGrow": "1", "height": "100%", "marginLeft": "4px", "marginRight": "4px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "7px", "paddingLeft": "12px", "paddingRight": "12px", "paddingTop": "7px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ykj0pitjtfiq9c", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "height": "100%", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ykp0sujs0m2lezn", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(92, 91, 94)", "boxSizing": "border-box", "color": "rgb(92, 91, 94)", "cursor": "pointer", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "12px", "letterSpacing": "1.2px", "lineHeight": "12.72px", "outlineColor": "rgb(92, 91, 94)", "textAlign": "left", "textDecoration": "rgb(92, 91, 94)", "textDecorationColor": "rgb(92, 91, 94)", "textEmphasisColor": "rgb(92, 91, 94)", "textTransform": "uppercase", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "About"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2"}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ykv0ozp62na3ib", "tagName": "li", "responsiveStyles": {"large": {"boxSizing": "border-box", "display": "list-item", "flexGrow": "1", "textAlign": "left"}}, "properties": {"aria-current": "false"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yl10fipg3bpvol", "tagName": "div", "responsiveStyles": {"large": {"boxSizing": "border-box", "height": "100%", "textAlign": "left"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yl707rimrduhh0l", "tagName": "a", "responsiveStyles": {"large": {"backgroundAttachment": "scroll, scroll", "backgroundClip": "border-box, border-box", "backgroundColor": "rgba(117, 117, 117, 0)", "backgroundImage": "linear-gradient(rgb(252, 252, 255), rgb(252, 252, 255)), none", "backgroundOrigin": "padding-box, padding-box", "backgroundPosition": "0% 50%, 0% 0%", "backgroundPositionX": "0%, 0%", "backgroundPositionY": "50%, 0%", "backgroundRepeat": "repeat, repeat", "backgroundSize": "auto, auto", "borderBottomStyle": "solid", "borderColor": "rgb(92, 91, 94) rgb(92, 91, 94) rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(92, 91, 94)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRadius": "4px 5px 0px 0px", "borderRight": "0.8px solid rgb(92, 91, 94)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(92, 91, 94)", "borderTopLeftRadius": "4px", "borderTopRightRadius": "5px", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px 0.8px 0px", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "flexBasis": "0%", "flexGrow": "1", "height": "100%", "marginLeft": "4px", "outlineColor": "rgb(0, 0, 238)", "paddingBottom": "7px", "paddingLeft": "12px", "paddingRight": "12px", "paddingTop": "7px", "position": "relative", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {"target": "_self", "tabindex": "0"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yld04io3vg7hdfw", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "borderColor": "rgb(0, 0, 238)", "boxSizing": "border-box", "color": "rgb(0, 0, 238)", "cursor": "pointer", "display": "flex", "height": "100%", "justifyContent": "flex-end", "outlineColor": "rgb(0, 0, 238)", "textAlign": "left", "textDecoration": "rgb(0, 0, 238)", "textDecorationColor": "rgb(0, 0, 238)", "textEmphasisColor": "rgb(0, 0, 238)"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ylj0ihqbu1izkgd", "tagName": "span", "responsiveStyles": {"large": {"borderColor": "rgb(92, 91, 94)", "boxSizing": "border-box", "color": "rgb(92, 91, 94)", "cursor": "pointer", "fontFamily": "\"space grotesk\", sans-serif", "fontSize": "12px", "letterSpacing": "1.2px", "lineHeight": "12.72px", "outlineColor": "rgb(92, 91, 94)", "textAlign": "left", "textDecoration": "rgb(92, 91, 94)", "textDecorationColor": "rgb(92, 91, 94)", "textEmphasisColor": "rgb(92, 91, 94)", "textTransform": "uppercase", "textWrap": "nowrap", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "Contact"}}}]}]}], "linkUrl": "https://dbasantia55.wixstudio.com/my-site-2/contact"}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ylp0wgmmgz9la4e", "tagName": "div", "responsiveStyles": {"large": {"borderBottomStyle": "solid", "borderColor": "rgba(0, 0, 0, 0)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "bottom": "0px", "boxSizing": "border-box", "display": "flex", "justifyContent": "space-between", "left": "0px", "pointerEvents": "none", "position": "absolute", "right": "0px", "top": "0px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ylv07urmzse8z2p", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "backgroundColor": "rgb(252, 252, 255)", "borderBottom": "0.8px solid rgb(38, 38, 39)", "borderBottomStyle": "solid", "borderBottomWidth": "0.8px", "borderColor": "rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRight": "0.8px solid rgb(38, 38, 39)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(38, 38, 39)", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px", "boxSizing": "border-box", "cursor": "pointer", "display": "flex", "justifyContent": "center", "opacity": "0", "overflowX": "hidden", "overflowY": "hidden", "paddingLeft": "10px", "paddingRight": "10px", "pointerEvents": "none", "transform": "matrix(-1, 0, 0, 1, 0, 0)"}}, "properties": {"aria-hidden": "true", "aria-label": "scroll"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ylz01moxrhm<PERSON><PERSON>", "tagName": "span", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "pointer", "fill": "rgb(38, 38, 39)", "height": "16px", "maxHeight": "100%", "maxWidth": "100%", "minWidth": "1px", "pointerEvents": "none", "width": "16px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ym303dft586dlm4", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 6 12\" style=\"\n  box-sizing: border-box;\n  cursor: pointer;\n  display: inline;\n  fill: rgb(38, 38, 39);\n  height: 16px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  pointer-events: none;\n  width: 16px;\"><path d=\"M6 6L.8 0 0 .7 4.7 6 0 11.3l.8.7z\"></path></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "pointer", "display": "inline", "fill": "rgb(38, 38, 39)", "height": "16px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "pointerEvents": "none", "width": "16px"}}, "properties": {"xmlns": "http://www.w3.org/2000/svg", "viewBox": "0 0 6 12"}, "children": []}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ym60av1voboaehl", "tagName": "div", "responsiveStyles": {"large": {"alignItems": "center", "backgroundColor": "rgb(252, 252, 255)", "borderBottom": "0.8px solid rgb(38, 38, 39)", "borderBottomStyle": "solid", "borderBottomWidth": "0.8px", "borderColor": "rgb(38, 38, 39)", "borderLeft": "0.8px solid rgb(38, 38, 39)", "borderLeftStyle": "solid", "borderLeftWidth": "0.8px", "borderRight": "0.8px solid rgb(38, 38, 39)", "borderRightStyle": "solid", "borderRightWidth": "0.8px", "borderStyle": "solid", "borderTop": "0.8px solid rgb(38, 38, 39)", "borderTopStyle": "solid", "borderTopWidth": "0.8px", "borderWidth": "0.8px", "boxSizing": "border-box", "cursor": "pointer", "display": "flex", "justifyContent": "center", "opacity": "0", "overflowX": "hidden", "overflowY": "hidden", "paddingLeft": "10px", "paddingRight": "10px", "pointerEvents": "none"}}, "properties": {"aria-hidden": "true", "aria-label": "scroll"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ymb0fcaljq9q01s", "tagName": "span", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "pointer", "fill": "rgb(38, 38, 39)", "height": "16px", "maxHeight": "100%", "maxWidth": "100%", "minWidth": "1px", "pointerEvents": "none", "width": "16px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ymf0asm7xrjs1v", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 6 12\" style=\"\n  box-sizing: border-box;\n  cursor: pointer;\n  display: inline;\n  fill: rgb(38, 38, 39);\n  height: 16px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  pointer-events: none;\n  width: 16px;\"><path d=\"M6 6L.8 0 0 .7 4.7 6 0 11.3l.8.7z\"></path></svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"boxSizing": "border-box", "cursor": "pointer", "display": "inline", "fill": "rgb(38, 38, 39)", "height": "16px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "pointerEvents": "none", "width": "16px"}}, "properties": {"xmlns": "http://www.w3.org/2000/svg", "viewBox": "0 0 6 12"}, "children": []}]}]}]}]}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ymi012uwfhtfqwx", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "center", "boxSizing": "border-box", "display": "none", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "gridTemplate": "1fr / 1fr", "gridTemplateRows": "1fr", "height": "35px", "justifySelf": "end", "marginBottom": "0%", "marginLeft": "0%", "marginRight": "2.99927%", "marginTop": "0%", "maxHeight": "99999px", "maxWidth": "99999px", "position": "relative", "width": "35px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ymm0htqcvojrmv", "tagName": "nav", "responsiveStyles": {"large": {"display": "none", "height": "35px"}}, "properties": {"aria-label": "Site"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ymp0mspftkvg04", "tagName": "div", "responsiveStyles": {"large": {"alignSelf": "stretch", "boxSizing": "border-box", "cursor": "pointer", "display": "none", "gridArea": "1 / 1 / 2 / 2", "gridRow": "1 / 2", "height": "35px", "justifySelf": "stretch", "maxHeight": "99999px", "maxWidth": "99999px", "position": "relative", "touchAction": "manipulation"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ymt0ykpn9hh3av", "tagName": "button", "responsiveStyles": {"large": {"appearance": "auto", "backgroundColor": "rgba(255, 255, 255, 0)", "borderBottomStyle": "solid", "borderColor": "rgb(148, 148, 148)", "borderLeftStyle": "solid", "borderRightStyle": "solid", "borderStyle": "solid", "borderTopStyle": "solid", "boxSizing": "border-box", "cursor": "pointer", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "100%", "minHeight": "10px", "minWidth": "10px", "textAlign": "center", "touchAction": "manipulation", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.2s, 0s", "transitionProperty": "all, visibility", "transitionTimingFunction": "ease, ease", "width": "100%"}}, "properties": {"type": "button", "aria-expanded": "false", "aria-haspopup": "dialog", "aria-label": "MENU"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ymx0udf9zml225k", "tagName": "span", "responsiveStyles": {"large": {"alignItems": "center", "cursor": "pointer", "display": "flex", "flexDirection": "row-reverse", "flexFlow": "row-reverse nowrap", "flexGrow": "1", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "100%", "justifyContent": "center", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "center", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.2s, 0s", "transitionProperty": "all, visibility", "transitionTimingFunction": "ease, ease", "width": "100%"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yn10e8tj25fg49m", "tagName": "span", "responsiveStyles": {"large": {"cursor": "pointer", "display": "none", "fontFamily": "madefor-text", "fontSize": "16px", "lineHeight": "22.4px", "maxWidth": "100%", "minWidth": "28.8px", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "center", "textOverflow": "ellipsis", "textWrap": "nowrap", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.2s, 0s", "transitionProperty": "all, visibility", "transitionTimingFunction": "ease, ease", "whiteSpace": "nowrap"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "tagName": "span", "component": {"name": "Text", "options": {"text": "MENU"}}}]}, {"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yn605i77mqyxqq", "tagName": "span", "responsiveStyles": {"large": {"cursor": "pointer", "fill": "rgb(38, 38, 39)", "flexShrink": "0", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "60px", "minWidth": "1px", "textAlign": "center", "transitionBehavior": "normal, normal", "transitionDelay": "0s, 0s", "transitionDuration": "0.2s, 0s", "transitionProperty": "all, visibility", "transitionTimingFunction": "ease, ease", "width": "60px"}}, "properties": {"aria-hidden": "true"}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86ynb0m7lfrkwww3m", "tagName": "span", "responsiveStyles": {"large": {"cursor": "pointer", "display": "flex", "fill": "rgb(38, 38, 39)", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "60px", "textAlign": "center", "width": "60px"}}, "properties": {}, "children": [{"@type": "@builder.io/sdk:Element", "id": "builder-mfq86yni0y0e1l1f34k", "tagName": "div", "component": {"name": "Custom Code", "options": {"code": "<svg data-bbox=\"44 64 112 72\" viewBox=\"0 0 200 200\" height=\"200\" width=\"200\" xmlns=\"http://www.w3.org/2000/svg\" data-type=\"shape\" style=\"\n  cursor: pointer;\n  display: flex;\n  fill: rgb(38, 38, 39);\n  font-family: Arial;\n  font-size: 13.3333px;\n  height: 200px;\n  overflow-clip-margin: content-box;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  text-align: center;\n  width: 200px;\">\n    <g>\n        <path d=\"M156 64v6H44v-6h112z\"></path>\n        <path d=\"M156 97v6H44v-6h112z\"></path>\n        <path d=\"M156 130v6H44v-6h112z\"></path>\n    </g>\n</svg>"}}, "layerName": "SVG", "responsiveStyles": {"large": {"cursor": "pointer", "display": "flex", "fill": "rgb(38, 38, 39)", "fontFamily": "<PERSON><PERSON>", "fontSize": "13.3333px", "height": "200px", "overflowClipMargin": "content-box", "overflowX": "hidden", "overflowY": "hidden", "textAlign": "center", "width": "200px"}}, "properties": {"viewBox": "0 0 200 200", "height": "200", "width": "200", "xmlns": "http://www.w3.org/2000/svg"}, "children": []}]}]}]}]}]}]}]}]}]}]}]}]}